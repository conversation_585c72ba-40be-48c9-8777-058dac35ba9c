import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // SEO优化配置
  poweredByHeader: false, // 移除X-Powered-By头部
  compress: true, // 启用gzip压缩

  // 图片优化配置
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60,
  },

  // 实验性功能
  experimental: {
    optimizeCss: true,
  },
};

export default nextConfig;
