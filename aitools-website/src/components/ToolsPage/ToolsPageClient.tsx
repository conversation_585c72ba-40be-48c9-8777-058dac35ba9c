'use client';

import React, { useState, useEffect } from 'react';
import ToolCard from '@/components/ToolCard';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import LoginModal from '@/components/auth/LoginModal';
import { apiClient } from '@/lib/api';
import { TOOL_PRICING_OPTIONS } from '@/constants/pricing';
import { Search, Filter, Grid, List, ChevronDown } from 'lucide-react';

// 定义Tool类型
interface Tool {
  _id: string;
  name: string;
  description: string;
  website: string;
  logo?: string;
  category: string;
  tags: string[];
  pricing: 'free' | 'freemium' | 'paid';
  views: number;
  likes: number;
  publishedAt?: Date;
  status: string;
}

interface ToolsPageClientProps {
  initialTools: Tool[];
  totalCount: number;
}

const categories = [
  { value: '', label: '所有分类' },
  { value: 'text-generation', label: '文本生成' },
  { value: 'image-generation', label: '图像生成' },
  { value: 'code-generation', label: '代码生成' },
  { value: 'data-analysis', label: '数据分析' },
  { value: 'audio-processing', label: '音频处理' },
  { value: 'video-editing', label: '视频编辑' }
];

const pricingOptions = TOOL_PRICING_OPTIONS;

const sortOptions = [
  { value: 'popular', label: '最受欢迎' },
  { value: 'newest', label: '最新添加' },
  { value: 'name', label: '名称排序' },
  { value: 'views', label: '浏览量' }
];

export default function ToolsPageClient({ initialTools, totalCount }: ToolsPageClientProps) {
  const [tools, setTools] = useState<Tool[]>(initialTools);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedPricing, setSelectedPricing] = useState('');
  const [sortBy, setSortBy] = useState('popular');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  // 当筛选条件改变时重新获取数据
  useEffect(() => {
    if (searchTerm || selectedCategory || selectedPricing || sortBy !== 'popular') {
      fetchTools();
    }
  }, [searchTerm, selectedCategory, selectedPricing, sortBy]);

  const fetchTools = async () => {
    try {
      setLoading(true);
      setError('');

      const params: any = {
        status: 'published',
        limit: 50
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (selectedCategory) {
        params.category = selectedCategory;
      }

      if (selectedPricing) {
        params.pricing = selectedPricing;
      }

      // 排序逻辑
      switch (sortBy) {
        case 'newest':
          params.sort = 'publishedAt';
          params.order = 'desc';
          break;
        case 'name':
          params.sort = 'name';
          params.order = 'asc';
          break;
        case 'views':
          params.sort = 'views';
          params.order = 'desc';
          break;
        default: // popular
          params.sort = 'likes';
          params.order = 'desc';
          break;
      }

      const response = await apiClient.getTools(params);

      if (response.success && response.data) {
        setTools(response.data.tools);
      } else {
        setError('获取工具列表失败');
      }
    } catch (err) {
      console.error('Error fetching tools:', err);
      setError('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchTools();
  };

  const resetFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
    setSelectedPricing('');
    setSortBy('popular');
    setTools(initialTools);
  };

  return (
    <>
      {/* 搜索和筛选区域 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          {/* 搜索栏 */}
          <form onSubmit={handleSearch} className="mb-4">
            <div className="relative max-w-2xl mx-auto">
              <input
                type="text"
                placeholder="搜索 AI 工具..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
            </div>
          </form>

          {/* 筛选器和视图切换 */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex flex-wrap gap-4">
              {/* 分类筛选 */}
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {categories.map((category) => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>

              {/* 价格筛选 */}
              <select
                value={selectedPricing}
                onChange={(e) => setSelectedPricing(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">所有价格</option>
                {pricingOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>

              {/* 排序 */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {sortOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>

              {/* 重置按钮 */}
              <button
                type="button"
                onClick={resetFilters}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                重置
              </button>
            </div>

            {/* 视图切换 */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
              >
                <Grid className="h-5 w-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
              >
                <List className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 工具列表 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 结果统计 */}
        <div className="mb-6">
          <p className="text-gray-600">
            找到 <span className="font-semibold">{tools.length}</span> 个工具
            {totalCount > tools.length && (
              <span className="text-sm text-gray-500 ml-2">
                (共 {totalCount} 个)
              </span>
            )}
          </p>
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="mb-6">
            <ErrorMessage
              message={error}
              onClose={() => setError('')}
            />
          </div>
        )}

        {/* 加载状态 */}
        {loading && (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        )}

        {/* 工具网格/列表 */}
        {!loading && (
          <div className={
            viewMode === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
              : 'space-y-4'
          }>
            {tools.map((tool) => (
              <ToolCard
                key={tool._id}
                tool={tool}
                onLoginRequired={() => setIsLoginModalOpen(true)}
              />
            ))}
          </div>
        )}

        {/* 空状态 */}
        {!loading && tools.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Search className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              没有找到匹配的工具
            </h3>
            <p className="text-gray-600 mb-4">
              尝试调整搜索条件或筛选器
            </p>
            <button
              onClick={resetFilters}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              重置筛选器
            </button>
          </div>
        )}
      </div>

      {/* 登录模态框 */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </>
  );
}
