'use client';

import React, { useState } from 'react';
import ToolCard from '@/components/ToolCard';
import LoginModal from '@/components/auth/LoginModal';

// 定义Tool类型
interface Tool {
  _id: string;
  name: string;
  description: string;
  website: string;
  logo?: string;
  category: string;
  tags: string[];
  pricing: 'free' | 'freemium' | 'paid';
  views: number;
  likes: number;
  publishedAt?: Date;
  status: string;
}

interface HomePageClientProps {
  featuredTools: Tool[];
  todayTools: Tool[];
  recentTools: Tool[];
}

export default function HomePageClient({ 
  featuredTools, 
  todayTools, 
  recentTools 
}: HomePageClientProps) {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  return (
    <>
      {/* Featured Tools Section */}
      {featuredTools.length > 0 && (
        <section className="py-16 bg-white" aria-labelledby="featured-tools-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-12">
              <h2 id="featured-tools-heading" className="text-3xl font-bold text-gray-900 mb-4">
                <span className="inline-block mr-2 text-yellow-500" aria-hidden="true">⭐</span>
                热门推荐
              </h2>
              <p className="text-lg text-gray-600">
                最受欢迎的 AI 工具推荐
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" role="list" aria-label="热门推荐的AI工具">
              {featuredTools.map((tool) => (
                <article key={tool._id} role="listitem">
                  <ToolCard
                    tool={tool}
                    onLoginRequired={() => setIsLoginModalOpen(true)}
                  />
                </article>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Today's Tools Section */}
      {todayTools.length > 0 && (
        <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50" aria-labelledby="today-tools-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-12">
              <h2 id="today-tools-heading" className="text-3xl font-bold text-gray-900 mb-4">
                <span className="inline-block mr-2 text-green-600" aria-hidden="true">📅</span>
                今日发布
              </h2>
              <p className="text-lg text-gray-600">
                今天刚刚发布的最新 AI 工具
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" role="list" aria-label="今日发布的AI工具">
              {todayTools.map((tool) => (
                <article key={tool._id} role="listitem">
                  <ToolCard
                    tool={tool}
                    onLoginRequired={() => setIsLoginModalOpen(true)}
                  />
                </article>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Recent Tools Section */}
      {recentTools.length > 0 && (
        <section className="py-16 bg-white" aria-labelledby="recent-tools-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-12">
              <h2 id="recent-tools-heading" className="text-3xl font-bold text-gray-900 mb-4">
                <span className="inline-block mr-2 text-blue-600" aria-hidden="true">🕒</span>
                最近发布
              </h2>
              <p className="text-lg text-gray-600">
                过去一周发布的热门 AI 工具
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" role="list" aria-label="最近发布的AI工具">
              {recentTools.map((tool) => (
                <article key={tool._id} role="listitem">
                  <ToolCard
                    tool={tool}
                    onLoginRequired={() => setIsLoginModalOpen(true)}
                  />
                </article>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </>
  );
}
