import React from 'react';
import Link from 'next/link';
import { Metadata } from 'next';
import Layout from '@/components/Layout';
import CategoryCard from '@/components/CategoryCard';
import HomePageClient from '@/components/HomePage/HomePageClient';
import dbConnect from '@/lib/mongodb';
import ToolModel from '@/models/Tool';
import { getWebsiteStructuredData, getOrganizationStructuredData, getToolListStructuredData } from '@/lib/seo/structuredData';
import { Search, Star, Zap } from 'lucide-react';

// 定义Tool类型
interface ToolType {
  _id: string;
  name: string;
  description: string;
  website: string;
  logo?: string;
  category: string;
  tags: string[];
  pricing: 'free' | 'freemium' | 'paid';
  views: number;
  likes: number;
  publishedAt?: Date;
  status: string;
}

// 生成主页metadata
export const metadata: Metadata = {
  title: 'AI工具导航 - 发现最新最好的AI工具',
  description: '探索最新的AI工具和应用，包括文本生成、图像创作、代码编程、数据分析等。发现能提升您工作效率和创造力的AI工具。',
  keywords: 'AI工具, 人工智能, 文本生成, 图像生成, 代码生成, 数据分析, ChatGPT, Midjourney, AI导航',
  authors: [{ name: 'AI工具导航团队' }],
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',
    siteName: 'AI工具导航',
    title: 'AI工具导航 - 发现最新最好的AI工具',
    description: '探索最新的AI工具和应用，包括文本生成、图像创作、代码编程、数据分析等。发现能提升您工作效率和创造力的AI工具。',
    images: [
      {
        url: '/og-homepage.jpg',
        width: 1200,
        height: 630,
        alt: 'AI工具导航 - 发现最新最好的AI工具',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI工具导航 - 发现最新最好的AI工具',
    description: '探索最新的AI工具和应用，包括文本生成、图像创作、代码编程、数据分析等。',
    images: ['/og-homepage.jpg'],
  },
  alternates: {
    canonical: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',
  },
};

// 分类数据

const categories = [
  {
    _id: '1',
    name: '文本生成',
    slug: 'text-generation',
    description: 'AI tools for generating and editing text content',
    icon: '📝',
    color: '#3B82F6',
    toolCount: 25
  },
  {
    _id: '2',
    name: '图像生成',
    slug: 'image-generation',
    description: 'AI tools for creating and editing images',
    icon: '🎨',
    color: '#8B5CF6',
    toolCount: 18
  },
  {
    _id: '3',
    name: '代码生成',
    slug: 'code-generation',
    description: 'AI tools for writing and debugging code',
    icon: '💻',
    color: '#F59E0B',
    toolCount: 12
  },
  {
    _id: '4',
    name: '数据分析',
    slug: 'data-analysis',
    description: 'AI tools for analyzing and visualizing data',
    icon: '📊',
    color: '#06B6D4',
    toolCount: 15
  }
];

// 服务器端数据获取函数
async function getHomePageData() {
  try {
    await dbConnect();

    // 准备日期参数
    const today = new Date();
    const todayStart = new Date(today);
    todayStart.setHours(0, 0, 0, 0);
    const todayEnd = new Date(today);
    todayEnd.setHours(23, 59, 59, 999);

    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    sevenDaysAgo.setHours(0, 0, 0, 0);

    // 并行获取所有数据
    const [featuredTools, todayTools, recentTools] = await Promise.all([
      // 热门工具 - 按浏览量排序
      ToolModel.find({ status: 'published' })
        .sort({ views: -1 })
        .limit(6)
        .lean(),

      // 当日发布的工具
      ToolModel.find({
        status: 'published',
        publishedAt: {
          $gte: todayStart,
          $lte: todayEnd
        }
      })
        .sort({ publishedAt: -1 })
        .limit(6)
        .lean(),

      // 最近发布的工具
      ToolModel.find({
        status: 'published',
        publishedAt: {
          $gte: sevenDaysAgo
        }
      })
        .sort({ publishedAt: -1 })
        .limit(6)
        .lean()
    ]);

    // 序列化数据以避免Next.js序列化问题
    return {
      featuredTools: JSON.parse(JSON.stringify(featuredTools)) as ToolType[],
      todayTools: JSON.parse(JSON.stringify(todayTools)) as ToolType[],
      recentTools: JSON.parse(JSON.stringify(recentTools)) as ToolType[]
    };
  } catch (error) {
    console.error('Error fetching homepage data:', error);
    return {
      featuredTools: [] as ToolType[],
      todayTools: [] as ToolType[],
      recentTools: [] as ToolType[]
    };
  }
}

export default async function Home() {
  // 服务器端获取数据
  const { featuredTools, todayTools, recentTools } = await getHomePageData();

  // 生成结构化数据
  const websiteStructuredData = getWebsiteStructuredData();
  const organizationStructuredData = getOrganizationStructuredData();
  const allTools = [...featuredTools, ...todayTools, ...recentTools];
  const toolListStructuredData = allTools.length > 0 ? getToolListStructuredData(allTools as any) : null;

  return (
    <Layout>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteStructuredData)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationStructuredData)
        }}
      />
      {toolListStructuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(toolListStructuredData)
          }}
        />
      )}

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20" role="banner">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <header className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              发现最好的
              <span className="text-blue-600"> AI 工具</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美工具。
            </p>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto mb-8">
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索 AI 工具、分类或功能..."
                  className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-lg"
                />
                <Search className="absolute left-4 top-4 h-6 w-6 text-gray-400" />
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/tools"
                className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                <Zap className="mr-2 h-5 w-5" />
                浏览所有工具
              </Link>
              <Link
                href="/submit"
                className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                提交您的工具
              </Link>
            </div>
          </header>
        </div>
      </section>

      {/* 工具展示区域 - 使用客户端组件处理交互 */}
      <HomePageClient
        featuredTools={featuredTools}
        todayTools={todayTools}
        recentTools={recentTools}
      />

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              <Star className="inline-block mr-2 h-8 w-8 text-blue-600" />
              热门分类
            </h2>
            <p className="text-lg text-gray-600">
              按功能分类浏览 AI 工具
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categories.map((category) => (
              <CategoryCard key={category._id} category={category} />
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href="/categories"
              className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
            >
              查看所有分类
            </Link>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-white mb-2">500+</div>
              <div className="text-blue-100">AI 工具</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-white mb-2">50+</div>
              <div className="text-blue-100">工具分类</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-white mb-2">10K+</div>
              <div className="text-blue-100">用户访问</div>
            </div>
          </div>
        </div>
      </section>

    </Layout>
  );
}
