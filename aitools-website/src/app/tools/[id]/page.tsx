import React from 'react';
import Link from 'next/link';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Layout from '@/components/Layout';
import ToolDetailClient from '@/components/tools/ToolDetailClient';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import { getToolStructuredData, getBreadcrumbStructuredData } from '@/lib/seo/structuredData';

// 生成动态metadata
export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
  try {
    await dbConnect();
    const { id } = await params;

    const tool = await Tool.findById(id).lean();

    if (!tool) {
      return {
        title: '工具不存在 - AI工具导航',
        description: '您访问的AI工具不存在或已被删除。',
      };
    }

    const title = `${tool.name} - AI工具导航`;
    const description = tool.description || `${tool.name}是一个优秀的AI工具，帮助您提升工作效率。`;
    const keywords = [tool.name, ...(tool.tags || []), 'AI工具', tool.category].join(', ');
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com';
    const canonical = `/tools/${tool._id}`;
    const ogImage = tool.logo || '/og-tool-default.jpg';

    return {
      title,
      description,
      keywords,
      authors: [{ name: 'AI工具导航团队' }],
      robots: {
        index: true,
        follow: true,
      },
      openGraph: {
        type: 'article',
        locale: 'zh_CN',
        url: `${baseUrl}${canonical}`,
        siteName: 'AI工具导航',
        title,
        description,
        images: [
          {
            url: ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`,
            width: 1200,
            height: 630,
            alt: `${tool.name} - AI工具`,
          },
        ],
        publishedTime: tool.publishedAt?.toISOString(),
        modifiedTime: tool.updatedAt?.toISOString(),
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: [ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`],
      },
      alternates: {
        canonical: `${baseUrl}${canonical}`,
      },
    };
  } catch (error) {
    return {
      title: '工具详情 - AI工具导航',
      description: '查看AI工具的详细信息和使用指南。',
    };
  }
}

// 服务器端渲染的工具详情页
export default async function ToolDetailPage({ params }: { params: Promise<{ id: string }> }) {
  try {
    await dbConnect();
    const { id } = await params;

    const tool = await Tool.findById(id).lean();

    if (!tool || tool.status !== 'published') {
      notFound();
    }

    // 转换为客户端可用的格式
    const serializedTool = {
      ...tool,
      _id: tool._id.toString(),
      submittedAt: tool.submittedAt.toISOString(),
      publishedAt: tool.publishedAt?.toISOString(),
      updatedAt: tool.updatedAt?.toISOString(),
    };

    // 生成结构化数据
    const toolStructuredData = getToolStructuredData(serializedTool as any);
    const breadcrumbStructuredData = getBreadcrumbStructuredData([
      { name: '首页', url: '/' },
      { name: '工具目录', url: '/tools' },
      { name: tool.name, url: `/tools/${tool._id}` }
    ]);

    return (
      <Layout>
        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(toolStructuredData)
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(breadcrumbStructuredData)
          }}
        />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 面包屑导航 */}
          <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-6" aria-label="面包屑导航">
            <Link href="/" className="hover:text-blue-600">首页</Link>
            <span>/</span>
            <Link href="/tools" className="hover:text-blue-600">工具目录</Link>
            <span>/</span>
            <span className="text-gray-900">{tool.name}</span>
          </nav>

          {/* 返回按钮 */}
          <div className="mb-6">
            <Link
              href="/tools"
              className="inline-flex items-center text-blue-600 hover:text-blue-700"
            >
              <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              返回工具目录
            </Link>
          </div>

          {/* 工具详情客户端组件 */}
          <ToolDetailClient initialTool={serializedTool as any} toolId={id} />
        </div>
      </Layout>
    );
  } catch (error) {
    console.error('Error loading tool:', error);
    notFound();
  }
}
