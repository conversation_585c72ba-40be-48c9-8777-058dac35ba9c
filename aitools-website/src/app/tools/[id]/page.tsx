'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import Layout from '@/components/Layout';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import LikeButton from '@/components/tools/LikeButton';
import CommentSection from '@/components/tools/CommentSection';
import LoginModal from '@/components/auth/LoginModal';
import { apiClient, Tool } from '@/lib/api';
import { getToolPricingColor, getToolPricingText } from '@/constants/pricing';
import {
  ExternalLink,
  Heart,
  Eye,
  Tag,
  DollarSign,
  ArrowLeft,
  Share2
} from 'lucide-react';

// 工具详情页面组件

export default function ToolDetailPage() {
  const params = useParams();
  const [tool, setTool] = useState<Tool | null>(null);
  const [relatedTools, setRelatedTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  useEffect(() => {
    if (params.id) {
      fetchToolDetails(params.id as string);
    }
  }, [params.id]);

  const fetchToolDetails = async (toolId: string) => {
    try {
      setLoading(true);
      setError('');

      const response = await apiClient.getTool(toolId);

      if (response.success && response.data) {
        setTool(response.data);
        // 获取相关工具
        fetchRelatedTools(response.data.category);
      } else {
        setError(response.error || '工具不存在');
      }
    } catch (err) {
      setError('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  };

  const fetchRelatedTools = async (category: string) => {
    try {
      const response = await apiClient.getTools({
        category,
        status: 'approved',
        limit: 3
      });

      if (response.success && response.data) {
        // 排除当前工具
        const filtered = response.data.tools.filter(t => t._id !== params.id);
        setRelatedTools(filtered.slice(0, 3));
      }
    } catch (err) {
      // 静默失败，相关工具不是必需的
    }
  };



  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <LoadingSpinner size="lg" />
        </div>
      </Layout>
    );
  }

  if (error || !tool) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <ErrorMessage
            message={error || '工具不存在'}
            onClose={() => setError('')}
          />
          <div className="text-center mt-8">
            <Link
              href="/tools"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回工具目录
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <div className="flex items-center space-x-2 text-sm text-gray-500 mb-6">
          <Link href="/" className="hover:text-blue-600">首页</Link>
          <span>/</span>
          <Link href="/tools" className="hover:text-blue-600">工具目录</Link>
          <span>/</span>
          <span className="text-gray-900">{tool.name}</span>
        </div>

        {/* Back Button */}
        <div className="mb-6">
          <Link
            href="/tools"
            className="inline-flex items-center text-blue-600 hover:text-blue-700"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回工具目录
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Tool Header */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-center space-x-4">
                  {tool.logo ? (
                    <img
                      src={tool.logo}
                      alt={tool.name}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-2xl">
                        {tool.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                      {tool.name}
                    </h1>
                    {tool.tagline && (
                      <p className="text-lg text-gray-600 mb-3">
                        {tool.tagline}
                      </p>
                    )}
                    <div className="flex items-center space-x-4">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getToolPricingColor(tool.pricing)}`}>
                        <DollarSign className="mr-1 h-4 w-4" />
                        {getToolPricingText(tool.pricing)}
                      </span>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Eye className="h-4 w-4" />
                          <span>{tool.views || 0} 浏览</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Heart className="h-4 w-4" />
                          <span>{tool.likes || 0} 喜欢</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <LikeButton
                    toolId={tool._id}
                    initialLikes={tool.likes}
                    onLoginRequired={() => setIsLoginModalOpen(true)}
                  />
                  <button className="p-2 text-gray-400 hover:text-blue-500 transition-colors">
                    <Share2 className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {/* Description */}
              <p className="text-gray-600 text-lg leading-relaxed mb-6">
                {tool.description}
              </p>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-6">
                {tool.tags?.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer"
                  >
                    <Tag className="mr-1 h-3 w-3" />
                    {tag}
                  </span>
                ))}
              </div>

              {/* CTA Button */}
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href={tool.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <ExternalLink className="mr-2 h-5 w-5" />
                  访问 {tool.name}
                </a>
                <LikeButton
                  toolId={tool._id}
                  initialLikes={tool.likes}
                  onLoginRequired={() => setIsLoginModalOpen(true)}
                />
              </div>
            </div>



            {/* Tool Info - 暂时隐藏pros和cons，因为API中没有这些字段 */}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Tool Info */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">工具信息</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">分类</span>
                  <span className="text-gray-900 font-medium">文本生成</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">价格模式</span>
                  <span className={`px-2 py-1 rounded text-sm font-medium ${getToolPricingColor(tool.pricing)}`}>
                    {getToolPricingText(tool.pricing)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">提交日期</span>
                  <span className="text-gray-900">{tool.submittedAt || tool.createdAt}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">提交者</span>
                  <span className="text-gray-900">{tool.submittedBy || '未知'}</span>
                </div>
              </div>
            </div>

            {/* Related Tools */}
            {relatedTools.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">相关工具</h3>
                <div className="space-y-4">
                    {relatedTools.map((relatedTool) => (
                      <div key={relatedTool._id} className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
                        <Link href={`/tools/${relatedTool._id}`}>
                          <h4 className="font-medium text-gray-900 hover:text-blue-600 mb-1">
                            {relatedTool.name}
                          </h4>
                          <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                            {relatedTool.description}
                          </p>
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <span className={`px-2 py-1 rounded ${getToolPricingColor(relatedTool.pricing)}`}>
                              {getToolPricingText(relatedTool.pricing)}
                            </span>
                            <div className="flex items-center space-x-2">
                              <span>{relatedTool.views || 0} 浏览</span>
                              <span>{relatedTool.likes || 0} 喜欢</span>
                            </div>
                          </div>
                        </Link>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Comments Section */}
              <div className="mt-12">
                <CommentSection
                  toolId={tool._id}
                  onLoginRequired={() => setIsLoginModalOpen(true)}
                />
              </div>
          </div>
        </div>
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </Layout>
  );
}
