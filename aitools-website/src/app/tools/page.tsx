import React from 'react';
import { Metadata } from 'next';
import Layout from '@/components/Layout';
import ToolsPageClient from '@/components/ToolsPage/ToolsPageClient';
import dbConnect from '@/lib/mongodb';
import ToolModel from '@/models/Tool';

// 定义Tool类型
interface ToolType {
  _id: string;
  name: string;
  description: string;
  website: string;
  logo?: string;
  category: string;
  tags: string[];
  pricing: 'free' | 'freemium' | 'paid';
  views: number;
  likes: number;
  publishedAt?: Date;
  status: string;
}

// SEO元数据
export const metadata: Metadata = {
  title: 'AI工具目录 - 发现最新最好的AI工具',
  description: '浏览我们精选的AI工具目录，包含500+个最新的人工智能工具。按分类、价格和功能筛选，找到适合您需求的完美AI工具。',
  keywords: 'AI工具, 人工智能工具, AI工具目录, 机器学习工具, 深度学习工具, AI应用',
  openGraph: {
    title: 'AI工具目录 - 发现最新最好的AI工具',
    description: '浏览我们精选的AI工具目录，包含500+个最新的人工智能工具。按分类、价格和功能筛选，找到适合您需求的完美AI工具。',
    type: 'website',
    images: [
      {
        url: '/og-tools-directory.jpg',
        width: 1200,
        height: 630,
        alt: 'AI工具目录'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI工具目录 - 发现最新最好的AI工具',
    description: '浏览我们精选的AI工具目录，包含500+个最新的人工智能工具。',
    images: ['/og-tools-directory.jpg']
  },
  alternates: {
    canonical: '/tools'
  }
};

// 服务器端数据获取函数
async function getToolsData() {
  try {
    await dbConnect();

    // 获取所有已发布的工具，按受欢迎程度排序
    const tools = await ToolModel.find({ status: 'published' })
      .sort({ likes: -1, views: -1 })
      .limit(50)
      .lean();

    // 获取总数
    const totalCount = await ToolModel.countDocuments({ status: 'published' });

    // 序列化数据
    return {
      tools: JSON.parse(JSON.stringify(tools)) as ToolType[],
      totalCount
    };
  } catch (error) {
    console.error('Error fetching tools data:', error);
    return {
      tools: [] as ToolType[],
      totalCount: 0
    };
  }
}

export default async function ToolsPage() {
  // 服务器端获取数据
  const { tools, totalCount } = await getToolsData();

  return (
    <Layout>
      {/* 页面标题 */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">AI工具目录</h1>
          <p className="text-xl text-gray-600">
            发现 {totalCount} 个精选的AI工具，提升您的工作效率和创造力
          </p>
        </div>
      </div>

      {/* 工具列表客户端组件 */}
      <ToolsPageClient
        initialTools={tools}
        totalCount={totalCount}
      />
    </Layout>
  );
}
