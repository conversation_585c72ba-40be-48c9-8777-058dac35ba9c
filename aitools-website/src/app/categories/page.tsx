import React from 'react';
import { Metadata } from 'next';
import Layout from '@/components/Layout';
import CategoryCard from '@/components/CategoryCard';
import dbConnect from '@/lib/mongodb';
import ToolModel from '@/models/Tool';
import { Grid, TrendingUp } from 'lucide-react';

// SEO元数据
export const metadata: Metadata = {
  title: 'AI工具分类 - 按功能浏览AI工具',
  description: '按功能分类浏览AI工具，包括文本生成、图像生成、代码生成、数据分析等50+个分类。快速找到您需要的AI工具类型。',
  keywords: 'AI工具分类, 人工智能分类, AI工具类型, 机器学习工具分类, AI应用分类',
  openGraph: {
    title: 'AI工具分类 - 按功能浏览AI工具',
    description: '按功能分类浏览AI工具，包括文本生成、图像生成、代码生成、数据分析等50+个分类。',
    type: 'website',
    images: [
      {
        url: '/og-categories.jpg',
        width: 1200,
        height: 630,
        alt: 'AI工具分类'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI工具分类 - 按功能浏览AI工具',
    description: '按功能分类浏览AI工具，包括文本生成、图像生成、代码生成等50+个分类。',
    images: ['/og-categories.jpg']
  },
  alternates: {
    canonical: '/categories'
  }
};

interface Category {
  _id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  toolCount: number;
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await apiClient.getCategories();

      if (response.success && response.data) {
        // 转换API数据格式为组件期望的格式
        const transformedCategories = response.data.categories.map((apiCategory: any) => {
          // 分类元数据映射
          const categoryMetadata: Record<string, { description: string; icon: string; color: string }> = {
            'text-generation': {
              description: '利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等',
              icon: '📝',
              color: '#3B82F6'
            },
            'image-generation': {
              description: '使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等',
              icon: '🎨',
              color: '#10B981'
            },
            'code-generation': {
              description: '智能代码生成和编程辅助工具，提高开发效率',
              icon: '💻',
              color: '#8B5CF6'
            },
            'data-analysis': {
              description: '数据分析和可视化工具，帮助洞察数据价值',
              icon: '📊',
              color: '#F59E0B'
            },
            'audio-processing': {
              description: '音频处理、语音合成、音乐生成等音频AI工具',
              icon: '🎵',
              color: '#EF4444'
            },
            'video-editing': {
              description: '视频编辑、特效制作、自动剪辑等视频处理工具',
              icon: '🎬',
              color: '#06B6D4'
            },
            'translation': {
              description: '多语言翻译和本地化工具，打破语言障碍',
              icon: '🌐',
              color: '#84CC16'
            },
            'search-engines': {
              description: '智能搜索引擎和信息检索工具',
              icon: '🔍',
              color: '#6366F1'
            },
            'education': {
              description: '教育学习辅助工具，个性化学习体验',
              icon: '📚',
              color: '#EC4899'
            },
            'marketing': {
              description: '营销自动化、内容营销、广告优化等营销工具',
              icon: '📈',
              color: '#F97316'
            },
            'productivity': {
              description: '提高工作效率的各类生产力工具',
              icon: '⚡',
              color: '#22D3EE'
            },
            'customer-service': {
              description: '客户服务自动化、聊天机器人等客服工具',
              icon: '🤝',
              color: '#A855F7'
            }
          };

          const metadata = categoryMetadata[apiCategory.id] || {
            description: '优质AI工具集合',
            icon: '🔧',
            color: '#6B7280'
          };

          return {
            _id: apiCategory.id,
            name: apiCategory.name,
            slug: apiCategory.id,
            description: metadata.description,
            icon: metadata.icon,
            color: metadata.color,
            toolCount: apiCategory.count
          };
        });

        setCategories(transformedCategories);
      }
    } catch (err) {
      setError('获取分类列表失败，请稍后重试');
      console.error('Error fetching categories:', err);
    } finally {
      setLoading(false);
    }
  };

  const popularCategories = categories
    .sort((a, b) => b.toolCount - a.toolCount)
    .slice(0, 6);

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center min-h-[400px]">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <ErrorMessage message={error} />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            <Grid className="inline-block mr-3 h-10 w-10 text-blue-600" />
            AI 工具分类
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            按功能分类浏览我们精选的 AI 工具集合。每个分类都包含经过验证的高质量工具，帮助您快速找到所需的解决方案。
          </p>
        </div>

        {/* Stats */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-100 rounded-lg p-8 mb-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {categories.length}
              </div>
              <div className="text-gray-700">个分类</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {categories.reduce((sum, cat) => sum + cat.toolCount, 0)}
              </div>
              <div className="text-gray-700">个工具</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {categories.length > 0 ? Math.round(categories.reduce((sum, cat) => sum + cat.toolCount, 0) / categories.length) : 0}
              </div>
              <div className="text-gray-700">平均每分类工具数</div>
            </div>
          </div>
        </div>

        {/* Popular Categories */}
        <section className="mb-16">
          <div className="flex items-center mb-8">
            <TrendingUp className="h-8 w-8 text-blue-600 mr-3" />
            <h2 className="text-3xl font-bold text-gray-900">热门分类</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {popularCategories.map((category) => (
              <CategoryCard key={category._id} category={category} />
            ))}
          </div>
        </section>

        {/* All Categories */}
        <section>
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900">所有分类</h2>
            <div className="text-sm text-gray-600">
              {categories.length} 个分类
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {categories.map((category) => (
              <CategoryCard key={category._id} category={category} />
            ))}
          </div>
        </section>

        {/* CTA Section */}
        <section className="mt-16 bg-blue-600 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold text-white mb-4">
            没有找到您需要的分类？
          </h2>
          <p className="text-blue-100 mb-6">
            我们持续添加新的工具和分类。如果您有建议或想要提交新工具，请联系我们。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/submit"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-gray-50 transition-colors"
            >
              提交新工具
            </a>
            <a
              href="/contact"
              className="inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-lg text-white hover:bg-blue-700 transition-colors"
            >
              联系我们
            </a>
          </div>
        </section>
      </div>
    </Layout>
  );
}
