[{"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx": "1", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx": "2", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx": "3", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts": "4", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts": "5", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts": "6", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts": "7", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts": "8", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts": "9", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts": "10", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts": "11", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts": "12", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts": "13", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts": "14", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts": "15", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts": "16", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts": "17", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx": "18", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx": "19", "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx": "20", "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx": "21", "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx": "22", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx": "23", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx": "24", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx": "25", "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx": "26", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx": "27", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx": "28", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx": "29", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx": "30", "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx": "31", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx": "32", "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx": "33", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx": "34", "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx": "35", "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx": "36", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx": "37", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx": "38", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx": "39", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx": "40", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx": "41", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx": "42", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx": "43", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx": "44", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts": "45", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts": "46", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts": "47", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts": "48", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts": "49", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts": "50", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts": "51", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts": "52", "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts": "53", "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts": "54", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts": "55", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts": "56", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts": "57", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts": "58", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts": "59", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts": "60", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts": "61", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts": "62", "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx": "63", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx": "64", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx": "65", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx": "66", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx": "67", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx": "68", "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx": "69", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts": "70", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts": "71", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx": "72", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts": "73", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts": "74"}, {"size": 12560, "mtime": 1750914757080, "results": "75", "hashOfConfig": "76"}, {"size": 17223, "mtime": 1750914371854, "results": "77", "hashOfConfig": "76"}, {"size": 14947, "mtime": 1751014762901, "results": "78", "hashOfConfig": "76"}, {"size": 5321, "mtime": 1750906802986, "results": "79", "hashOfConfig": "76"}, {"size": 1926, "mtime": 1751014639961, "results": "80", "hashOfConfig": "76"}, {"size": 2074, "mtime": 1750918572984, "results": "81", "hashOfConfig": "76"}, {"size": 3121, "mtime": 1751014815428, "results": "82", "hashOfConfig": "76"}, {"size": 171, "mtime": 1750921851894, "results": "83", "hashOfConfig": "76"}, {"size": 3714, "mtime": 1750921931408, "results": "84", "hashOfConfig": "76"}, {"size": 4489, "mtime": 1750930430193, "results": "85", "hashOfConfig": "76"}, {"size": 2376, "mtime": 1750906822203, "results": "86", "hashOfConfig": "76"}, {"size": 4403, "mtime": 1750924179468, "results": "87", "hashOfConfig": "76"}, {"size": 5614, "mtime": 1750951694652, "results": "88", "hashOfConfig": "76"}, {"size": 7170, "mtime": 1751016131915, "results": "89", "hashOfConfig": "76"}, {"size": 4740, "mtime": 1751016315107, "results": "90", "hashOfConfig": "76"}, {"size": 5538, "mtime": 1750952541605, "results": "91", "hashOfConfig": "76"}, {"size": 2484, "mtime": 1750938669998, "results": "92", "hashOfConfig": "76"}, {"size": 14489, "mtime": 1750918356378, "results": "93", "hashOfConfig": "76"}, {"size": 8836, "mtime": 1750919626197, "results": "94", "hashOfConfig": "76"}, {"size": 14185, "mtime": 1751018576923, "results": "95", "hashOfConfig": "76"}, {"size": 915, "mtime": 1750923464798, "results": "96", "hashOfConfig": "76"}, {"size": 11754, "mtime": 1750944837061, "results": "97", "hashOfConfig": "76"}, {"size": 7642, "mtime": 1750951120332, "results": "98", "hashOfConfig": "76"}, {"size": 10389, "mtime": 1750945315721, "results": "99", "hashOfConfig": "76"}, {"size": 20955, "mtime": 1751014907413, "results": "100", "hashOfConfig": "76"}, {"size": 21004, "mtime": 1750945519465, "results": "101", "hashOfConfig": "76"}, {"size": 16243, "mtime": 1751018388834, "results": "102", "hashOfConfig": "76"}, {"size": 4543, "mtime": 1750930937103, "results": "103", "hashOfConfig": "76"}, {"size": 11853, "mtime": 1751018146931, "results": "104", "hashOfConfig": "76"}, {"size": 9754, "mtime": 1751018434009, "results": "105", "hashOfConfig": "76"}, {"size": 1425, "mtime": 1750903550616, "results": "106", "hashOfConfig": "76"}, {"size": 845, "mtime": 1750908285683, "results": "107", "hashOfConfig": "76"}, {"size": 3063, "mtime": 1750925211983, "results": "108", "hashOfConfig": "76"}, {"size": 505, "mtime": 1750908273441, "results": "109", "hashOfConfig": "76"}, {"size": 863, "mtime": 1750908296528, "results": "110", "hashOfConfig": "76"}, {"size": 5768, "mtime": 1750942157899, "results": "111", "hashOfConfig": "76"}, {"size": 4214, "mtime": 1751018010231, "results": "112", "hashOfConfig": "76"}, {"size": 9109, "mtime": 1750930558601, "results": "113", "hashOfConfig": "76"}, {"size": 6661, "mtime": 1750945557905, "results": "114", "hashOfConfig": "76"}, {"size": 4438, "mtime": 1750923424688, "results": "115", "hashOfConfig": "76"}, {"size": 867, "mtime": 1750922283437, "results": "116", "hashOfConfig": "76"}, {"size": 362, "mtime": 1750922147686, "results": "117", "hashOfConfig": "76"}, {"size": 8935, "mtime": 1750924218629, "results": "118", "hashOfConfig": "76"}, {"size": 3198, "mtime": 1750951009317, "results": "119", "hashOfConfig": "76"}, {"size": 2449, "mtime": 1750942881883, "results": "120", "hashOfConfig": "76"}, {"size": 7052, "mtime": 1751014618384, "results": "121", "hashOfConfig": "76"}, {"size": 5059, "mtime": 1750930729612, "results": "122", "hashOfConfig": "76"}, {"size": 921, "mtime": 1750903252798, "results": "123", "hashOfConfig": "76"}, {"size": 6818, "mtime": 1750903357994, "results": "124", "hashOfConfig": "76"}, {"size": 1667, "mtime": 1750903308052, "results": "125", "hashOfConfig": "76"}, {"size": 2141, "mtime": 1750921803605, "results": "126", "hashOfConfig": "76"}, {"size": 5025, "mtime": 1751014604563, "results": "127", "hashOfConfig": "76"}, {"size": 3406, "mtime": 1750921782108, "results": "128", "hashOfConfig": "76"}, {"size": 720, "mtime": 1750903327281, "results": "129", "hashOfConfig": "76"}, {"size": 3866, "mtime": 1750984404444, "results": "130", "hashOfConfig": "76"}, {"size": 2238, "mtime": 1750995712168, "results": "131", "hashOfConfig": "76"}, {"size": 4057, "mtime": 1751018497440, "results": "132", "hashOfConfig": "76"}, {"size": 5242, "mtime": 1751013821668, "results": "133", "hashOfConfig": "76"}, {"size": 1022, "mtime": 1750984456438, "results": "134", "hashOfConfig": "76"}, {"size": 11755, "mtime": 1751017957312, "results": "135", "hashOfConfig": "76"}, {"size": 2237, "mtime": 1750949131424, "results": "136", "hashOfConfig": "76"}, {"size": 3202, "mtime": 1750953105864, "results": "137", "hashOfConfig": "76"}, {"size": 7991, "mtime": 1750984356171, "results": "138", "hashOfConfig": "76"}, {"size": 6764, "mtime": 1751005731451, "results": "139", "hashOfConfig": "76"}, {"size": 4621, "mtime": 1751005744888, "results": "140", "hashOfConfig": "76"}, {"size": 12431, "mtime": 1751004268664, "results": "141", "hashOfConfig": "76"}, {"size": 3833, "mtime": 1751018220215, "results": "142", "hashOfConfig": "76"}, {"size": 7687, "mtime": 1751017905894, "results": "143", "hashOfConfig": "76"}, {"size": 3527, "mtime": 1751018284048, "results": "144", "hashOfConfig": "76"}, {"size": 3985, "mtime": 1751017840303, "results": "145", "hashOfConfig": "76"}, {"size": 3989, "mtime": 1750984256539, "results": "146", "hashOfConfig": "76"}, {"size": 23256, "mtime": 1751018327971, "results": "147", "hashOfConfig": "76"}, {"size": 3508, "mtime": 1751014666579, "results": "148", "hashOfConfig": "76"}, {"size": 4411, "mtime": 1751017799346, "results": "149", "hashOfConfig": "76"}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "eypjqx", {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx", ["372", "373", "374", "375", "376", "377", "378", "379", "380", "381"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx", ["382", "383", "384", "385", "386", "387"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx", ["388", "389", "390", "391", "392"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts", ["393", "394"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts", ["395", "396"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts", ["397"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts", ["398", "399"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts", ["400", "401", "402"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts", ["403"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx", ["404"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx", ["405"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx", ["406", "407"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx", ["408"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx", ["409", "410"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx", ["411", "412", "413"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx", ["414"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx", ["415", "416", "417", "418", "419", "420"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx", ["421"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx", ["422", "423", "424", "425"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx", ["426", "427", "428", "429"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx", ["430"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx", ["431", "432", "433"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx", ["434", "435"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx", ["436", "437", "438", "439"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts", ["440"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts", ["441", "442", "443", "444"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts", ["445"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts", ["446"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts", ["447"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts", ["448", "449"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts", ["450", "451", "452", "453"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx", ["454", "455", "456", "457", "458"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx", ["459", "460"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx", ["461", "462"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx", ["463", "464", "465"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx", ["466"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx", ["467"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx", ["468"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts", [], [], {"ruleId": "469", "severity": 2, "message": "470", "line": 11, "column": 3, "nodeType": null, "messageId": "471", "endLine": 11, "endColumn": 8}, {"ruleId": "469", "severity": 2, "message": "472", "line": 16, "column": 3, "nodeType": null, "messageId": "471", "endLine": 16, "endColumn": 8}, {"ruleId": "469", "severity": 2, "message": "473", "line": 17, "column": 3, "nodeType": null, "messageId": "471", "endLine": 17, "endColumn": 11}, {"ruleId": "469", "severity": 2, "message": "474", "line": 20, "column": 3, "nodeType": null, "messageId": "471", "endLine": 20, "endColumn": 7}, {"ruleId": "469", "severity": 2, "message": "475", "line": 25, "column": 7, "nodeType": null, "messageId": "471", "endLine": 25, "endColumn": 21}, {"ruleId": "476", "severity": 1, "message": "477", "line": 48, "column": 6, "nodeType": "478", "endLine": 48, "endColumn": 17, "suggestions": "479"}, {"ruleId": "469", "severity": 2, "message": "480", "line": 62, "column": 14, "nodeType": null, "messageId": "471", "endLine": 62, "endColumn": 17}, {"ruleId": "469", "severity": 2, "message": "481", "line": 69, "column": 9, "nodeType": null, "messageId": "471", "endLine": 69, "endColumn": 19}, {"ruleId": "469", "severity": 2, "message": "482", "line": 78, "column": 9, "nodeType": null, "messageId": "471", "endLine": 78, "endColumn": 24}, {"ruleId": "469", "severity": 2, "message": "483", "line": 91, "column": 9, "nodeType": null, "messageId": "471", "endLine": 91, "endColumn": 27}, {"ruleId": "469", "severity": 2, "message": "484", "line": 17, "column": 3, "nodeType": null, "messageId": "471", "endLine": 17, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "485", "line": 61, "column": 6, "nodeType": "478", "endLine": 61, "endColumn": 20, "suggestions": "486"}, {"ruleId": "469", "severity": 2, "message": "480", "line": 78, "column": 14, "nodeType": null, "messageId": "471", "endLine": 78, "endColumn": 17}, {"ruleId": "469", "severity": 2, "message": "480", "line": 111, "column": 14, "nodeType": null, "messageId": "471", "endLine": 111, "endColumn": 17}, {"ruleId": "469", "severity": 2, "message": "480", "line": 137, "column": 14, "nodeType": null, "messageId": "471", "endLine": 137, "endColumn": 17}, {"ruleId": "487", "severity": 1, "message": "488", "line": 304, "column": 27, "nodeType": "489", "endLine": 308, "endColumn": 29}, {"ruleId": "469", "severity": 2, "message": "490", "line": 74, "column": 9, "nodeType": null, "messageId": "471", "endLine": 74, "endColumn": 15}, {"ruleId": "469", "severity": 2, "message": "491", "line": 88, "column": 14, "nodeType": null, "messageId": "471", "endLine": 88, "endColumn": 19}, {"ruleId": "469", "severity": 2, "message": "491", "line": 104, "column": 14, "nodeType": null, "messageId": "471", "endLine": 104, "endColumn": 19}, {"ruleId": "487", "severity": 1, "message": "488", "line": 173, "column": 15, "nodeType": "489", "endLine": 177, "endColumn": 17}, {"ruleId": "487", "severity": 1, "message": "488", "line": 267, "column": 19, "nodeType": "489", "endLine": 272, "endColumn": 21}, {"ruleId": "492", "severity": 2, "message": "493", "line": 19, "column": 18, "nodeType": "494", "messageId": "495", "endLine": 19, "endColumn": 21, "suggestions": "496"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 55, "column": 22, "nodeType": "494", "messageId": "495", "endLine": 55, "endColumn": 25, "suggestions": "497"}, {"ruleId": "469", "severity": 2, "message": "498", "line": 8, "column": 27, "nodeType": null, "messageId": "471", "endLine": 8, "endColumn": 34}, {"ruleId": "492", "severity": 2, "message": "493", "line": 96, "column": 23, "nodeType": "494", "messageId": "495", "endLine": 96, "endColumn": 26, "suggestions": "499"}, {"ruleId": "469", "severity": 2, "message": "498", "line": 6, "column": 27, "nodeType": null, "messageId": "471", "endLine": 6, "endColumn": 34}, {"ruleId": "492", "severity": 2, "message": "493", "line": 174, "column": 20, "nodeType": "494", "messageId": "495", "endLine": 174, "endColumn": 23, "suggestions": "500"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 212, "column": 70, "nodeType": "494", "messageId": "495", "endLine": 212, "endColumn": 73, "suggestions": "501"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 22, "column": 18, "nodeType": "494", "messageId": "495", "endLine": 22, "endColumn": 21, "suggestions": "502"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 59, "column": 22, "nodeType": "494", "messageId": "495", "endLine": 59, "endColumn": 25, "suggestions": "503"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 161, "column": 70, "nodeType": "494", "messageId": "495", "endLine": 161, "endColumn": 73, "suggestions": "504"}, {"ruleId": "469", "severity": 2, "message": "491", "line": 56, "column": 14, "nodeType": null, "messageId": "471", "endLine": 56, "endColumn": 19}, {"ruleId": "476", "severity": 1, "message": "505", "line": 54, "column": 6, "nodeType": "478", "endLine": 54, "endColumn": 19, "suggestions": "506"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 41, "column": 82, "nodeType": "494", "messageId": "495", "endLine": 41, "endColumn": 85, "suggestions": "507"}, {"ruleId": "469", "severity": 2, "message": "475", "line": 22, "column": 7, "nodeType": null, "messageId": "471", "endLine": 22, "endColumn": 21}, {"ruleId": "469", "severity": 2, "message": "480", "line": 109, "column": 14, "nodeType": null, "messageId": "471", "endLine": 109, "endColumn": 17}, {"ruleId": "469", "severity": 2, "message": "480", "line": 122, "column": 14, "nodeType": null, "messageId": "471", "endLine": 122, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "508", "line": 55, "column": 6, "nodeType": "478", "endLine": 55, "endColumn": 49, "suggestions": "509"}, {"ruleId": "469", "severity": 2, "message": "480", "line": 70, "column": 14, "nodeType": null, "messageId": "471", "endLine": 70, "endColumn": 17}, {"ruleId": "469", "severity": 2, "message": "510", "line": 11, "column": 3, "nodeType": null, "messageId": "471", "endLine": 11, "endColumn": 7}, {"ruleId": "469", "severity": 2, "message": "511", "line": 18, "column": 3, "nodeType": null, "messageId": "471", "endLine": 18, "endColumn": 7}, {"ruleId": "487", "severity": 1, "message": "488", "line": 97, "column": 19, "nodeType": "489", "endLine": 101, "endColumn": 21}, {"ruleId": "469", "severity": 2, "message": "491", "line": 104, "column": 14, "nodeType": null, "messageId": "471", "endLine": 104, "endColumn": 19}, {"ruleId": "469", "severity": 2, "message": "512", "line": 12, "column": 3, "nodeType": null, "messageId": "471", "endLine": 12, "endColumn": 7}, {"ruleId": "469", "severity": 2, "message": "513", "line": 15, "column": 3, "nodeType": null, "messageId": "471", "endLine": 15, "endColumn": 6}, {"ruleId": "469", "severity": 2, "message": "480", "line": 94, "column": 14, "nodeType": null, "messageId": "471", "endLine": 94, "endColumn": 17}, {"ruleId": "469", "severity": 2, "message": "480", "line": 111, "column": 14, "nodeType": null, "messageId": "471", "endLine": 111, "endColumn": 17}, {"ruleId": "469", "severity": 2, "message": "480", "line": 128, "column": 14, "nodeType": null, "messageId": "471", "endLine": 128, "endColumn": 17}, {"ruleId": "487", "severity": 1, "message": "488", "line": 197, "column": 23, "nodeType": "489", "endLine": 201, "endColumn": 25}, {"ruleId": "487", "severity": 1, "message": "488", "line": 300, "column": 21, "nodeType": "489", "endLine": 304, "endColumn": 23}, {"ruleId": "469", "severity": 2, "message": "491", "line": 27, "column": 14, "nodeType": null, "messageId": "471", "endLine": 27, "endColumn": 19}, {"ruleId": "469", "severity": 2, "message": "491", "line": 46, "column": 14, "nodeType": null, "messageId": "471", "endLine": 46, "endColumn": 19}, {"ruleId": "492", "severity": 2, "message": "493", "line": 63, "column": 42, "nodeType": "494", "messageId": "495", "endLine": 63, "endColumn": 45, "suggestions": "514"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 64, "column": 42, "nodeType": "494", "messageId": "495", "endLine": 64, "endColumn": 45, "suggestions": "515"}, {"ruleId": "476", "severity": 1, "message": "516", "line": 38, "column": 6, "nodeType": "478", "endLine": 38, "endColumn": 17, "suggestions": "517"}, {"ruleId": "469", "severity": 2, "message": "480", "line": 54, "column": 14, "nodeType": null, "messageId": "471", "endLine": 54, "endColumn": 17}, {"ruleId": "469", "severity": 2, "message": "480", "line": 74, "column": 14, "nodeType": null, "messageId": "471", "endLine": 74, "endColumn": 17}, {"ruleId": "487", "severity": 1, "message": "488", "line": 144, "column": 21, "nodeType": "489", "endLine": 148, "endColumn": 23}, {"ruleId": "487", "severity": 1, "message": "488", "line": 36, "column": 15, "nodeType": "489", "endLine": 40, "endColumn": 17}, {"ruleId": "469", "severity": 2, "message": "491", "line": 44, "column": 14, "nodeType": null, "messageId": "471", "endLine": 44, "endColumn": 19}, {"ruleId": "469", "severity": 2, "message": "491", "line": 84, "column": 14, "nodeType": null, "messageId": "471", "endLine": 84, "endColumn": 19}, {"ruleId": "469", "severity": 2, "message": "491", "line": 111, "column": 14, "nodeType": null, "messageId": "471", "endLine": 111, "endColumn": 19}, {"ruleId": "487", "severity": 1, "message": "488", "line": 61, "column": 13, "nodeType": "489", "endLine": 65, "endColumn": 15}, {"ruleId": "487", "severity": 1, "message": "488", "line": 93, "column": 21, "nodeType": "489", "endLine": 97, "endColumn": 23}, {"ruleId": "469", "severity": 2, "message": "518", "line": 5, "column": 27, "nodeType": null, "messageId": "471", "endLine": 5, "endColumn": 34}, {"ruleId": "469", "severity": 2, "message": "519", "line": 5, "column": 36, "nodeType": null, "messageId": "471", "endLine": 5, "endColumn": 46}, {"ruleId": "476", "severity": 1, "message": "520", "line": 55, "column": 6, "nodeType": "478", "endLine": 55, "endColumn": 14, "suggestions": "521"}, {"ruleId": "487", "severity": 1, "message": "488", "line": 156, "column": 13, "nodeType": "489", "endLine": 160, "endColumn": 15}, {"ruleId": "492", "severity": 2, "message": "493", "line": 4, "column": 34, "nodeType": "494", "messageId": "495", "endLine": 4, "endColumn": 37, "suggestions": "522"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 65, "column": 60, "nodeType": "494", "messageId": "495", "endLine": 65, "endColumn": 63, "suggestions": "523"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 145, "column": 31, "nodeType": "494", "messageId": "495", "endLine": 145, "endColumn": 34, "suggestions": "524"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 151, "column": 26, "nodeType": "494", "messageId": "495", "endLine": 151, "endColumn": 29, "suggestions": "525"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 152, "column": 26, "nodeType": "494", "messageId": "495", "endLine": 152, "endColumn": 29, "suggestions": "526"}, {"ruleId": "469", "severity": 2, "message": "527", "line": 1, "column": 8, "nodeType": null, "messageId": "471", "endLine": 1, "endColumn": 16}, {"ruleId": "469", "severity": 2, "message": "528", "line": 27, "column": 13, "nodeType": null, "messageId": "471", "endLine": 27, "endColumn": 26}, {"ruleId": "469", "severity": 2, "message": "529", "line": 5, "column": 8, "nodeType": null, "messageId": "471", "endLine": 5, "endColumn": 12}, {"ruleId": "469", "severity": 2, "message": "530", "line": 89, "column": 11, "nodeType": null, "messageId": "471", "endLine": 89, "endColumn": 14}, {"ruleId": "492", "severity": 2, "message": "493", "line": 158, "column": 25, "nodeType": "494", "messageId": "495", "endLine": 158, "endColumn": 28, "suggestions": "531"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 38, "column": 18, "nodeType": "494", "messageId": "495", "endLine": 38, "endColumn": 21, "suggestions": "532"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 51, "column": 22, "nodeType": "494", "messageId": "495", "endLine": 51, "endColumn": 25, "suggestions": "533"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 88, "column": 52, "nodeType": "494", "messageId": "495", "endLine": 88, "endColumn": 55, "suggestions": "534"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 89, "column": 52, "nodeType": "494", "messageId": "495", "endLine": 89, "endColumn": 55, "suggestions": "535"}, {"ruleId": "469", "severity": 2, "message": "536", "line": 18, "column": 17, "nodeType": null, "messageId": "471", "endLine": 18, "endColumn": 24}, {"ruleId": "492", "severity": 2, "message": "493", "line": 19, "column": 38, "nodeType": "494", "messageId": "495", "endLine": 19, "endColumn": 41, "suggestions": "537"}, {"ruleId": "476", "severity": 1, "message": "538", "line": 35, "column": 6, "nodeType": "478", "endLine": 35, "endColumn": 23, "suggestions": "539"}, {"ruleId": "469", "severity": 2, "message": "480", "line": 61, "column": 14, "nodeType": null, "messageId": "471", "endLine": 61, "endColumn": 17}, {"ruleId": "469", "severity": 2, "message": "480", "line": 85, "column": 14, "nodeType": null, "messageId": "471", "endLine": 85, "endColumn": 17}, {"ruleId": "492", "severity": 2, "message": "493", "line": 16, "column": 36, "nodeType": "494", "messageId": "495", "endLine": 16, "endColumn": 39, "suggestions": "540"}, {"ruleId": "476", "severity": 1, "message": "541", "line": 32, "column": 6, "nodeType": "478", "endLine": 32, "endColumn": 22, "suggestions": "542"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 18, "column": 36, "nodeType": "494", "messageId": "495", "endLine": 18, "endColumn": 39, "suggestions": "543"}, {"ruleId": "476", "severity": 1, "message": "541", "line": 32, "column": 6, "nodeType": "478", "endLine": 32, "endColumn": 22, "suggestions": "544"}, {"ruleId": "469", "severity": 2, "message": "536", "line": 12, "column": 17, "nodeType": null, "messageId": "471", "endLine": 12, "endColumn": 24}, {"ruleId": "492", "severity": 2, "message": "493", "line": 13, "column": 36, "nodeType": "494", "messageId": "495", "endLine": 13, "endColumn": 39, "suggestions": "545"}, {"ruleId": "476", "severity": 1, "message": "541", "line": 28, "column": 6, "nodeType": "478", "endLine": 28, "endColumn": 22, "suggestions": "546"}, {"ruleId": "469", "severity": 2, "message": "491", "line": 81, "column": 14, "nodeType": null, "messageId": "471", "endLine": 81, "endColumn": 19}, {"ruleId": "469", "severity": 2, "message": "480", "line": 55, "column": 14, "nodeType": null, "messageId": "471", "endLine": 55, "endColumn": 17}, {"ruleId": "487", "severity": 1, "message": "488", "line": 491, "column": 19, "nodeType": "489", "endLine": 495, "endColumn": 21}, "@typescript-eslint/no-unused-vars", "'Users' is defined but never used.", "unusedVar", "'Heart' is defined but never used.", "'Calendar' is defined but never used.", "'Star' is defined but never used.", "'categoryLabels' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", "ArrayExpression", ["547"], "'err' is defined but never used.", "'formatDate' is assigned a value but never used.", "'getActivityIcon' is assigned a value but never used.", "'getActivityBgColor' is assigned a value but never used.", "'MoreHorizontal' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["548"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'params' is assigned a value but never used.", "'error' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["549", "550"], ["551", "552"], "'request' is defined but never used.", ["553", "554"], ["555", "556"], ["557", "558"], ["559", "560"], ["561", "562"], ["563", "564"], "React Hook useEffect has a missing dependency: 'fetchCategoryData'. Either include it or remove the dependency array.", ["565"], ["566", "567"], "React Hook useEffect has a missing dependency: 'filterTools'. Either include it or remove the dependency array.", ["568"], "'User' is defined but never used.", "'Edit' is defined but never used.", "'Mail' is defined but never used.", "'Eye' is defined but never used.", ["569", "570"], ["571", "572"], "React Hook useEffect has a missing dependency: 'fetchToolDetails'. Either include it or remove the dependency array.", ["573"], "'FaHeart' is defined but never used.", "'FaRegHeart' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["574"], ["575", "576"], ["577", "578"], ["579", "580"], ["581", "582"], ["583", "584"], "'mongoose' is defined but never used.", "'paymentMethod' is assigned a value but never used.", "'Tool' is defined but never used.", "'now' is assigned a value but never used.", ["585", "586"], ["587", "588"], ["589", "590"], ["591", "592"], ["593", "594"], "'session' is assigned a value but never used.", ["595", "596"], "React Hook useEffect has missing dependencies: 'fetchOrderInfo' and 'router'. Either include them or remove the dependency array.", ["597"], ["598", "599"], "React Hook useEffect has missing dependencies: 'fetchToolInfo' and 'router'. Either include them or remove the dependency array.", ["600"], ["601", "602"], ["603"], ["604", "605"], ["606"], {"desc": "607", "fix": "608"}, {"desc": "609", "fix": "610"}, {"messageId": "611", "fix": "612", "desc": "613"}, {"messageId": "614", "fix": "615", "desc": "616"}, {"messageId": "611", "fix": "617", "desc": "613"}, {"messageId": "614", "fix": "618", "desc": "616"}, {"messageId": "611", "fix": "619", "desc": "613"}, {"messageId": "614", "fix": "620", "desc": "616"}, {"messageId": "611", "fix": "621", "desc": "613"}, {"messageId": "614", "fix": "622", "desc": "616"}, {"messageId": "611", "fix": "623", "desc": "613"}, {"messageId": "614", "fix": "624", "desc": "616"}, {"messageId": "611", "fix": "625", "desc": "613"}, {"messageId": "614", "fix": "626", "desc": "616"}, {"messageId": "611", "fix": "627", "desc": "613"}, {"messageId": "614", "fix": "628", "desc": "616"}, {"messageId": "611", "fix": "629", "desc": "613"}, {"messageId": "614", "fix": "630", "desc": "616"}, {"desc": "631", "fix": "632"}, {"messageId": "611", "fix": "633", "desc": "613"}, {"messageId": "614", "fix": "634", "desc": "616"}, {"desc": "635", "fix": "636"}, {"messageId": "611", "fix": "637", "desc": "613"}, {"messageId": "614", "fix": "638", "desc": "616"}, {"messageId": "611", "fix": "639", "desc": "613"}, {"messageId": "614", "fix": "640", "desc": "616"}, {"desc": "641", "fix": "642"}, {"desc": "643", "fix": "644"}, {"messageId": "611", "fix": "645", "desc": "613"}, {"messageId": "614", "fix": "646", "desc": "616"}, {"messageId": "611", "fix": "647", "desc": "613"}, {"messageId": "614", "fix": "648", "desc": "616"}, {"messageId": "611", "fix": "649", "desc": "613"}, {"messageId": "614", "fix": "650", "desc": "616"}, {"messageId": "611", "fix": "651", "desc": "613"}, {"messageId": "614", "fix": "652", "desc": "616"}, {"messageId": "611", "fix": "653", "desc": "613"}, {"messageId": "614", "fix": "654", "desc": "616"}, {"messageId": "611", "fix": "655", "desc": "613"}, {"messageId": "614", "fix": "656", "desc": "616"}, {"messageId": "611", "fix": "657", "desc": "613"}, {"messageId": "614", "fix": "658", "desc": "616"}, {"messageId": "611", "fix": "659", "desc": "613"}, {"messageId": "614", "fix": "660", "desc": "616"}, {"messageId": "611", "fix": "661", "desc": "613"}, {"messageId": "614", "fix": "662", "desc": "616"}, {"messageId": "611", "fix": "663", "desc": "613"}, {"messageId": "614", "fix": "664", "desc": "616"}, {"messageId": "611", "fix": "665", "desc": "613"}, {"messageId": "614", "fix": "666", "desc": "616"}, {"desc": "667", "fix": "668"}, {"messageId": "611", "fix": "669", "desc": "613"}, {"messageId": "614", "fix": "670", "desc": "616"}, {"desc": "671", "fix": "672"}, {"messageId": "611", "fix": "673", "desc": "613"}, {"messageId": "614", "fix": "674", "desc": "616"}, {"desc": "671", "fix": "675"}, {"messageId": "611", "fix": "676", "desc": "613"}, {"messageId": "614", "fix": "677", "desc": "616"}, {"desc": "671", "fix": "678"}, "Update the dependencies array to be: [fetchStats, timeRange]", {"range": "679", "text": "680"}, "Update the dependencies array to be: [fetchTools, statusFilter]", {"range": "681", "text": "682"}, "suggestUnknown", {"range": "683", "text": "684"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "685", "text": "686"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "687", "text": "684"}, {"range": "688", "text": "686"}, {"range": "689", "text": "684"}, {"range": "690", "text": "686"}, {"range": "691", "text": "684"}, {"range": "692", "text": "686"}, {"range": "693", "text": "684"}, {"range": "694", "text": "686"}, {"range": "695", "text": "684"}, {"range": "696", "text": "686"}, {"range": "697", "text": "684"}, {"range": "698", "text": "686"}, {"range": "699", "text": "684"}, {"range": "700", "text": "686"}, "Update the dependencies array to be: [fetchCategoryData, params.slug]", {"range": "701", "text": "702"}, {"range": "703", "text": "684"}, {"range": "704", "text": "686"}, "Update the dependencies array to be: [filterTools, likedTools, searchQuery, selectedCategory]", {"range": "705", "text": "706"}, {"range": "707", "text": "684"}, {"range": "708", "text": "686"}, {"range": "709", "text": "684"}, {"range": "710", "text": "686"}, "Update the dependencies array to be: [fetchToolDetails, params.id]", {"range": "711", "text": "712"}, "Update the dependencies array to be: [fetchComments, toolId]", {"range": "713", "text": "714"}, {"range": "715", "text": "684"}, {"range": "716", "text": "686"}, {"range": "717", "text": "684"}, {"range": "718", "text": "686"}, {"range": "719", "text": "684"}, {"range": "720", "text": "686"}, {"range": "721", "text": "684"}, {"range": "722", "text": "686"}, {"range": "723", "text": "684"}, {"range": "724", "text": "686"}, {"range": "725", "text": "684"}, {"range": "726", "text": "686"}, {"range": "727", "text": "684"}, {"range": "728", "text": "686"}, {"range": "729", "text": "684"}, {"range": "730", "text": "686"}, {"range": "731", "text": "684"}, {"range": "732", "text": "686"}, {"range": "733", "text": "684"}, {"range": "734", "text": "686"}, {"range": "735", "text": "684"}, {"range": "736", "text": "686"}, "Update the dependencies array to be: [status, orderId, router, fetchOrderInfo]", {"range": "737", "text": "738"}, {"range": "739", "text": "684"}, {"range": "740", "text": "686"}, "Update the dependencies array to be: [fetchToolInfo, router, status, toolId]", {"range": "741", "text": "742"}, {"range": "743", "text": "684"}, {"range": "744", "text": "686"}, {"range": "745", "text": "742"}, {"range": "746", "text": "684"}, {"range": "747", "text": "686"}, {"range": "748", "text": "742"}, [1125, 1136], "[fetchStats, timeRange]", [1646, 1660], "[fetchTools, statusFilter]", [672, 675], "unknown", [672, 675], "never", [1551, 1554], [1551, 1554], [2591, 2594], [2591, 2594], [4322, 4325], [4322, 4325], [5313, 5316], [5313, 5316], [802, 805], [802, 805], [1657, 1660], [1657, 1660], [4180, 4183], [4180, 4183], [1651, 1664], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, params.slug]", [1084, 1087], [1084, 1087], [1532, 1575], "[filterTools, likedTools, searchQuery, selectedCategory]", [1763, 1766], [1763, 1766], [1818, 1821], [1818, 1821], [1163, 1174], "[fetchToolDetails, params.id]", [1439, 1447], "[fetchComments, toolId]", [137, 140], [137, 140], [2074, 2077], [2074, 2077], [4406, 4409], [4406, 4409], [4562, 4565], [4562, 4565], [4621, 4624], [4621, 4624], [4206, 4209], [4206, 4209], [1171, 1174], [1171, 1174], [1442, 1445], [1442, 1445], [2429, 2432], [2429, 2432], [2517, 2520], [2517, 2520], [751, 754], [751, 754], [1155, 1172], "[status, orderId, router, fetchOrderInfo]", [617, 620], [617, 620], [910, 926], "[fetchToolInfo, router, status, toolId]", [604, 607], [604, 607], [895, 911], [511, 514], [511, 514], [859, 875]]