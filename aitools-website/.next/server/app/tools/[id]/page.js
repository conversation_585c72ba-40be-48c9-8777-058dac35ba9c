(()=>{var e={};e.id=8664,e.ids=[8664],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15568:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var r=t(60687),a=t(43210),l=t(85814),i=t.n(l),n=t(16189),c=t(98402),o=t(33823),d=t(11011),x=t(7485),m=t(82136),p=t(23877);function h({toolId:e,onLoginRequired:s}){let{data:t}=(0,m.useSession)(),[l,i]=(0,a.useState)([]),[n,c]=(0,a.useState)(""),[o,d]=(0,a.useState)(null),[x,h]=(0,a.useState)(""),[u,g]=(0,a.useState)(!1),[j,f]=(0,a.useState)(!1),b=async()=>{g(!0);try{let s=await fetch(`/api/tools/${e}/comments`);if(s.ok){let e=await s.json();e.success&&i(e.data.comments)}}catch(e){console.error("Failed to fetch comments:",e)}finally{g(!1)}},y=async()=>{if(!t)return void s?.();if(n.trim()){f(!0);try{let s=await fetch(`/api/tools/${e}/comments`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:n.trim()})});if(s.ok)(await s.json()).success&&(c(""),b());else{let e=await s.json();console.error("Comment submission failed:",e.message)}}catch(e){console.error("Comment submission error:",e)}finally{f(!1)}}},v=async r=>{if(!t)return void s?.();if(x.trim()){f(!0);try{let s=await fetch(`/api/tools/${e}/comments`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:x.trim(),parentId:r})});if(s.ok)(await s.json()).success&&(h(""),d(null),b());else{let e=await s.json();console.error("Reply submission failed:",e.message)}}catch(e){console.error("Reply submission error:",e)}finally{f(!1)}}},N=e=>{let s=new Date(e),t=Math.floor((new Date().getTime()-s.getTime())/36e5);return t<1?"刚刚":t<24?`${t}小时前`:t<168?`${Math.floor(t/24)}天前`:s.toLocaleDateString("zh-CN")},w=({comment:e,isReply:s=!1})=>(0,r.jsx)("div",{className:`${s?"ml-8 border-l-2 border-gray-100 pl-4":""}`,children:(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:e.userId.image?(0,r.jsx)("img",{src:e.userId.image,alt:e.userId.name,className:"w-8 h-8 rounded-full"}):(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)(p.x$1,{className:"w-4 h-4 text-gray-600"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsx)("span",{className:"font-medium text-gray-900",children:e.userId.name}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:N(e.createdAt)})]}),(0,r.jsx)("p",{className:"text-gray-700 mb-2",children:e.content}),(0,r.jsx)("div",{className:"flex items-center gap-4",children:!s&&(0,r.jsxs)("button",{onClick:()=>d(o===e._id?null:e._id),className:"text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1",children:[(0,r.jsx)(p.w1Z,{className:"w-3 h-3"}),"回复"]})}),o===e._id&&(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsx)("textarea",{value:x,onChange:e=>h(e.target.value),placeholder:"写下你的回复...",className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:3,maxLength:1e3}),(0,r.jsxs)("div",{className:"flex justify-end gap-2 mt-2",children:[(0,r.jsx)("button",{onClick:()=>{d(null),h("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:"取消"}),(0,r.jsx)("button",{onClick:()=>v(e._id),disabled:j||!x.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:j?"发送中...":"发送"})]})]}),e.replies&&e.replies.length>0&&(0,r.jsx)("div",{className:"mt-4 space-y-4",children:e.replies.map(e=>(0,r.jsx)(w,{comment:e,isReply:!0},e._id))})]})]})});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["评论 (",l.length,")"]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("textarea",{value:n,onChange:e=>c(e.target.value),placeholder:t?"写下你的评论...":"请先登录后评论",className:"w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4,maxLength:1e3,disabled:!t}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[n.length,"/1000"]}),(0,r.jsx)("button",{onClick:y,disabled:j||!n.trim()||!t,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:j?"发送中...":"发表评论"})]})]}),u?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"text-gray-500 mt-2",children:"加载评论中..."})]}):0===l.length?(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-gray-500",children:"暂无评论，来发表第一条评论吧！"})}):(0,r.jsx)("div",{className:"space-y-6",children:l.map(e=>(0,r.jsx)(w,{comment:e},e._id))})]})}var u=t(48577);t(62185);var g=t(94865),j=t(28559),f=t(23928),b=t(13861),y=t(67760);let v=(0,t(62688).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var N=t(37360),w=t(25334);function k(){(0,n.useParams)();let[e,s]=(0,a.useState)(null),[t,l]=(0,a.useState)([]),[m,p]=(0,a.useState)(!0),[k,A]=(0,a.useState)(""),[_,C]=(0,a.useState)(!1);return m?(0,r.jsx)(c.A,{children:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(o.A,{size:"lg"})})}):k||!e?(0,r.jsx)(c.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)(d.A,{message:k||"工具不存在",onClose:()=>A("")}),(0,r.jsx)("div",{className:"text-center mt-8",children:(0,r.jsxs)(i(),{href:"/tools",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(j.A,{className:"w-4 h-4 mr-2"}),"返回工具目录"]})})]})}):(0,r.jsxs)(c.A,{children:[(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6",children:[(0,r.jsx)(i(),{href:"/",className:"hover:text-blue-600",children:"首页"}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)(i(),{href:"/tools",className:"hover:text-blue-600",children:"工具目录"}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)("span",{className:"text-gray-900",children:e.name})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)(i(),{href:"/tools",className:"inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,r.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"返回工具目录"]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[e.logo?(0,r.jsx)("img",{src:e.logo,alt:e.name,className:"w-16 h-16 rounded-lg object-cover"}):(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:e.name.charAt(0).toUpperCase()})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:e.name}),e.tagline&&(0,r.jsx)("p",{className:"text-lg text-gray-600 mb-3",children:e.tagline}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${(0,g.Ef)(e.pricing)}`,children:[(0,r.jsx)(f.A,{className:"mr-1 h-4 w-4"}),(0,g.mV)(e.pricing)]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(b.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[e.views||0," 浏览"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[e.likes||0," 喜欢"]})]})]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(x.A,{toolId:e._id,initialLikes:e.likes,onLoginRequired:()=>C(!0)}),(0,r.jsx)("button",{className:"p-2 text-gray-400 hover:text-blue-500 transition-colors",children:(0,r.jsx)(v,{className:"h-5 w-5"})})]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-lg leading-relaxed mb-6",children:e.description}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:e.tags?.map((e,s)=>(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer",children:[(0,r.jsx)(N.A,{className:"mr-1 h-3 w-3"}),e]},s))}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(w.A,{className:"mr-2 h-5 w-5"}),"访问 ",e.name]}),(0,r.jsx)(x.A,{toolId:e._id,initialLikes:e.likes,onLoginRequired:()=>C(!0)})]})]})}),(0,r.jsxs)("div",{className:"lg:col-span-1",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"工具信息"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"分类"}),(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:"文本生成"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"价格模式"}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-sm font-medium ${(0,g.Ef)(e.pricing)}`,children:(0,g.mV)(e.pricing)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"提交日期"}),(0,r.jsx)("span",{className:"text-gray-900",children:e.submittedAt||e.createdAt})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"提交者"}),(0,r.jsx)("span",{className:"text-gray-900",children:e.submittedBy||"未知"})]})]})]}),t.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"相关工具"}),(0,r.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow",children:(0,r.jsxs)(i(),{href:`/tools/${e._id}`,children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 hover:text-blue-600 mb-1",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,r.jsx)("span",{className:`px-2 py-1 rounded ${(0,g.Ef)(e.pricing)}`,children:(0,g.mV)(e.pricing)}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{children:[e.views||0," 浏览"]}),(0,r.jsxs)("span",{children:[e.likes||0," 喜欢"]})]})]})]})},e._id))})]}),(0,r.jsx)("div",{className:"mt-12",children:(0,r.jsx)(h,{toolId:e._id,onLoginRequired:()=>C(!0)})})]})]})]}),(0,r.jsx)(u.A,{isOpen:_,onClose:()=>C(!1)})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23928:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37360:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},61416:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65335:(e,s,t)=>{Promise.resolve().then(t.bind(t,61416))},70063:(e,s,t)=>{Promise.resolve().then(t.bind(t,15568))},73720:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>o});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let o={children:["",{children:["tools",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,61416)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/tools/[id]/page",pathname:"/tools/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,865,1658,6707,4527],()=>t(73720));module.exports=r})();