(()=>{var e={};e.id=5093,e.ids=[5093],e.modules={2964:(e,s,t)=>{"use strict";t.d(s,{A:()=>x});var a=t(60687);t(43210);var r=t(85814),l=t.n(r),i=t(25334),n=t(13861),d=t(67760),o=t(7485),c=t(94865);let x=({tool:e,onLoginRequired:s,onUnlike:t,isInLikedPage:r=!1})=>(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,a.jsx)("img",{src:e.logo,alt:e.name,className:"w-12 h-12 rounded-lg object-cover"}):(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:e.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${(0,c.Ef)(e.pricing)}`,children:(0,c.mV)(e.pricing)})]})]}),(0,a.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(i.A,{className:"h-5 w-5"})})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.tags.slice(0,3).map((e,s)=>(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e},s)),e.tags.length>3&&(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:["+",e.tags.length-3]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.views})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.likes})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{toolId:e._id,initialLikes:e.likes,onLoginRequired:s,onUnlike:t,isInLikedPage:r}),(0,a.jsx)(l(),{href:`/tools/${e._id}`,className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors",children:"查看详情"})]})]})]})})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6943:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15730:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let o={children:["",{children:["categories",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,50203)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/categories/[slug]/page",pathname:"/categories/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21960:(e,s,t)=>{Promise.resolve().then(t.bind(t,94253))},25366:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50203:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx","default")},58408:(e,s,t)=>{Promise.resolve().then(t.bind(t,50203))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78272:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},94253:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var a=t(60687),r=t(43210),l=t(85814),i=t.n(l),n=t(16189),d=t(98402),o=t(2964),c=t(33823),x=t(11011);t(62185);var m=t(28559),h=t(80462),u=t(78272),p=t(6943),g=t(25366);let b=[{value:"",label:"所有价格"},{value:"free",label:"免费"},{value:"freemium",label:"免费增值"},{value:"paid",label:"付费"}],j=[{value:"popular",label:"最受欢迎"},{value:"newest",label:"最新添加"},{value:"name",label:"名称排序"},{value:"views",label:"浏览量"}];function f(){(0,n.useParams)();let[e,s]=(0,r.useState)(null),[t,l]=(0,r.useState)([]),[f,v]=(0,r.useState)(!0),[w,y]=(0,r.useState)(""),[N,k]=(0,r.useState)(""),[A,C]=(0,r.useState)(""),[P,_]=(0,r.useState)("popular"),[M,q]=(0,r.useState)("grid"),[D,L]=(0,r.useState)(!1),z=[...t.filter(e=>{let s=e.name.toLowerCase().includes(N.toLowerCase())||e.description.toLowerCase().includes(N.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(N.toLowerCase())),t=!A||e.pricing===A;return s&&t})].sort((e,s)=>{switch(P){case"popular":return(s.likes||0)-(e.likes||0);case"views":return(s.views||0)-(e.views||0);case"name":return e.name.localeCompare(s.name);case"newest":if(e.createdAt&&s.createdAt)return new Date(s.createdAt).getTime()-new Date(e.createdAt).getTime();return 0;default:return 0}});return f?(0,a.jsx)(d.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(c.A,{size:"lg"})})}):w?(0,a.jsx)(d.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(x.A,{message:w})})}):e?(0,a.jsx)(d.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6",children:[(0,a.jsx)(i(),{href:"/",className:"hover:text-blue-600",children:"首页"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)(i(),{href:"/categories",className:"hover:text-blue-600",children:"分类"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)("span",{className:"text-gray-900",children:e.name})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)(i(),{href:"/categories",className:"inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"返回分类列表"]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-lg flex items-center justify-center text-3xl",style:{backgroundColor:e.color},children:(0,a.jsx)("span",{className:"text-white",children:e.icon})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:e.name}),(0,a.jsxs)("p",{className:"text-lg text-gray-600",children:[e.toolCount," 个工具"]})]})]}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)("input",{type:"text",placeholder:"在此分类中搜索工具...",value:N,onChange:e=>k(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)(h.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),(0,a.jsx)("div",{className:"md:hidden mb-4",children:(0,a.jsxs)("button",{onClick:()=>L(!D),className:"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"筛选选项",(0,a.jsx)(u.A,{className:`ml-2 h-4 w-4 transform ${D?"rotate-180":""}`})]})}),(0,a.jsxs)("div",{className:`grid grid-cols-1 md:grid-cols-3 gap-4 ${D?"block":"hidden md:grid"}`,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格"}),(0,a.jsx)("select",{value:A,onChange:e=>C(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:b.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"排序"}),(0,a.jsx)("select",{value:P,onChange:e=>_(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:j.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"视图"}),(0,a.jsxs)("div",{className:"flex rounded-lg border border-gray-300",children:[(0,a.jsx)("button",{onClick:()=>q("grid"),className:`flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${"grid"===M?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:(0,a.jsx)(p.A,{className:"h-4 w-4 mx-auto"})}),(0,a.jsx)("button",{onClick:()=>q("list"),className:`flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${"list"===M?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:(0,a.jsx)(g.A,{className:"h-4 w-4 mx-auto"})})]})]})]})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("p",{className:"text-gray-600",children:["显示 ",z.length," 个结果",N&&` 搜索 "${N}"`]})}),z.length>0?(0,a.jsx)("div",{className:"grid"===M?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:z.map(e=>(0,a.jsx)(o.A,{tool:e},e._id))}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(h.A,{className:"h-12 w-12 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"未找到匹配的工具"}),(0,a.jsx)("p",{className:"text-gray-600",children:"尝试调整搜索条件或筛选选项"})]}),(0,a.jsxs)("section",{className:"mt-16 bg-gray-50 rounded-lg p-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"相关分类"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(i(),{href:"/categories/image-generation",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFA8"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"图像生成"})]}),(0,a.jsxs)(i(),{href:"/categories/code-generation",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCBB"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"代码生成"})]}),(0,a.jsxs)(i(),{href:"/categories/data-analysis",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"数据分析"})]}),(0,a.jsxs)(i(),{href:"/categories/audio-processing",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFB5"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"音频处理"})]})]})]})]})}):(0,a.jsx)(d.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"分类未找到"}),(0,a.jsx)("p",{className:"text-gray-600",children:"请检查URL或返回分类列表"}),(0,a.jsx)(i(),{href:"/categories",className:"text-blue-600 hover:text-blue-700 mt-4 inline-block",children:"返回分类列表"})]})})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4243,865,1658,6707,4527],()=>t(15730));module.exports=a})();