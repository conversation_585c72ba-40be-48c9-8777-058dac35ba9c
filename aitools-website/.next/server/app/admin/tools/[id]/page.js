(()=>{var e={};e.id=2868,e.ids=[2868],e.modules={1549:(e,s,t)=>{Promise.resolve().then(t.bind(t,97392))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16256:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var r=t(60687),a=t(43210),l=t(16189),i=t(98402),d=t(48730),n=t(5336),c=t(35071),o=t(28559),m=t(23928),x=t(62688);let h=(0,x.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var p=t(25334),u=t(64398),g=t(37360),b=t(58869);let y=(0,x.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);var j=t(40228),f=t(43649);let v={id:"1",name:"AI写作助手",description:"基于GPT技术的智能写作工具，支持多种文体创作，提供实时语法检查和内容优化建议。这是一个功能强大的AI写作助手，能够帮助用户快速生成高质量的文章、报告、邮件等各种文本内容。工具采用最新的自然语言处理技术，具有强大的语言理解和生成能力。",website:"https://aiwriter.example.com",logo:"https://via.placeholder.com/128",category:"text-generation",pricing:"freemium",tags:["写作","GPT","内容创作","语法检查","文本生成"],features:["智能写作：基于AI的内容生成","语法检查：实时检测和修正语法错误","多语言支持：支持中文、英文等多种语言","模板库：提供丰富的写作模板","协作功能：支持团队协作编辑","导出功能：支持多种格式导出"],submitterName:"张三",submitterEmail:"<EMAIL>",submittedAt:"2024-06-25T10:30:00Z",status:"pending",publishDate:"2024-06-28",screenshots:["https://via.placeholder.com/600x400","https://via.placeholder.com/600x400","https://via.placeholder.com/600x400"]},N={"text-generation":"文本生成","image-generation":"图像生成","code-generation":"代码生成","data-analysis":"数据分析","audio-processing":"音频处理","video-editing":"视频编辑",translation:"语言翻译","search-engines":"搜索引擎",education:"教育学习",marketing:"营销工具",productivity:"生产力工具","customer-service":"客户服务"},w={free:"免费",freemium:"免费增值",paid:"付费"};function k(){(0,l.useParams)();let e=(0,l.useRouter)(),[s]=(0,a.useState)(v),[t,x]=(0,a.useState)(!1),[k,A]=(0,a.useState)(""),[P,C]=(0,a.useState)(!1),M=async()=>{C(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("工具已批准！"),e.push("/admin")}catch(e){alert("操作失败，请重试")}finally{C(!1)}},q=async()=>{if(k.trim()){C(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("工具已拒绝！"),e.push("/admin")}catch(e){alert("操作失败，请重试")}finally{C(!1),x(!1),A("")}}};return(0,r.jsx)(i.A,{children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"返回审核列表"]}),(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,r.jsx)("img",{src:s.logo,alt:s.name,className:"w-24 h-24 rounded-xl object-cover border border-gray-200 shadow-sm"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:s.name}),(e=>{switch(e){case"pending":return(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"待审核"]});case"approved":return(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"已通过审核"]});case"published":return(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"已发布"]});case"rejected":return(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"已拒绝"]});default:return null}})(s.status)]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600 mb-4",children:[(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:N[s.category]}),(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:[(0,r.jsx)(m.A,{className:"w-3 h-3 mr-1"}),w[s.pricing]]}),(0,r.jsxs)("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:[(0,r.jsx)(h,{className:"w-4 h-4 mr-1"}),"访问网站",(0,r.jsx)(p.A,{className:"w-3 h-3 ml-1"})]})]}),(0,r.jsx)("p",{className:"text-gray-600 max-w-3xl",children:s.description})]})]}),"pending"===s.status&&(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsxs)("button",{onClick:M,disabled:P,className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 mr-2"}),P?"处理中...":"批准"]}),(0,r.jsxs)("button",{onClick:()=>x(!0),disabled:P,className:"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"拒绝"]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"主要功能"}),(0,r.jsx)("ul",{className:"space-y-3",children:s.features.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)(u.A,{className:"w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,r.jsx)("span",{className:"text-gray-700",children:e})]},s))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"标签"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:s.tags.map((e,s)=>(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[(0,r.jsx)(g.A,{className:"w-3 h-3 mr-1"}),e]},s))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"截图预览"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.screenshots.map((e,t)=>(0,r.jsx)("img",{src:e,alt:`${s.name} 截图 ${t+1}`,className:"w-full h-48 object-cover rounded-lg border border-gray-200"},t))})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"提交信息"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:s.submitterName}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"提交者"})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(y,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:s.submitterEmail}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"联系邮箱"})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(j.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(s.submittedAt).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"提交时间"})]})]}),s.publishDate&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(s.publishDate).toLocaleDateString("zh-CN")}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"期望发布日期"})]})]})]})]}),(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(f.A,{className:"w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"审核指南"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"• 验证工具网站是否可正常访问"}),(0,r.jsx)("li",{children:"• 检查工具描述是否准确客观"}),(0,r.jsx)("li",{children:"• 确认分类和标签是否合适"}),(0,r.jsx)("li",{children:"• 评估工具质量和实用性"}),(0,r.jsx)("li",{children:"• 检查是否存在重复提交"})]})]})]})})]})]}),t&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"拒绝工具"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"请详细说明拒绝的原因，这将帮助提交者了解问题并改进他们的提交。"}),(0,r.jsx)("textarea",{value:k,onChange:e=>A(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入详细的拒绝原因..."}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,r.jsx)("button",{onClick:()=>{x(!1),A("")},disabled:P,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors",children:"取消"}),(0,r.jsx)("button",{onClick:q,disabled:!k.trim()||P,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:P?"处理中...":"确认拒绝"})]})]})})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23928:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25334:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31536:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),d=t(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(s,n);let c={children:["",{children:["admin",{children:["tools",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,97392)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/tools/[id]/page",pathname:"/admin/tools/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33873:e=>{"use strict";e.exports=require("path")},35071:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37360:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},43649:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},58869:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62688:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var r=t(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},d=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),n=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:o,...m},x)=>(0,r.createElement)("svg",{ref:x,...c,width:s,height:s,stroke:e,strokeWidth:a?24*Number(t)/Number(s):t,className:d("lucide",l),...!i&&!n(m)&&{"aria-hidden":"true"},...m},[...o.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(i)?i:[i]])),m=(e,s)=>{let t=(0,r.forwardRef)(({className:t,...l},n)=>(0,r.createElement)(o,{ref:n,iconNode:s,className:d(`lucide-${a(i(e))}`,`lucide-${e}`,t),...l}));return t.displayName=i(e),t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},82989:(e,s,t)=>{Promise.resolve().then(t.bind(t,16256))},97392:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx","default")},98402:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687);t(43210);var a=t(85814),l=t.n(a);let i=({children:e})=>(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("main",{className:"flex-1",children:e}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,865,1658,6707],()=>t(31536));module.exports=r})();