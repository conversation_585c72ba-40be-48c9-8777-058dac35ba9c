(()=>{var e={};e.id=3287,e.ids=[3287,6706],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9865:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>f,serverHooks:()=>x,workAsyncStorage:()=>I,workUnitAsyncStorage:()=>v});var n={};r.r(n),r.d(n,{POST:()=>m});var i=r(96559),a=r(48088),o=r(37719),s=r(32190),d=r(44999),c=r(75745),u=r(31098),l=r(30762),p=r(96706);async function m(e){try{let t,r=await e.text(),n=(await (0,d.headers)()).get("stripe-signature");if(!n)return s.NextResponse.json({error:"Missing stripe-signature header"},{status:400});let i=process.env.STRIPE_WEBHOOK_SECRET;if(!i)return console.error("STRIPE_WEBHOOK_SECRET is not configured"),s.NextResponse.json({error:"Webhook secret not configured"},{status:500});try{t=(0,p.ZW)(r,n,i)}catch(e){return console.error("Webhook signature verification failed:",e),s.NextResponse.json({error:"Invalid signature"},{status:400})}switch(await (0,c.A)(),t.type){case"payment_intent.succeeded":await y(t.data.object);break;case"payment_intent.payment_failed":await g(t.data.object);break;case"payment_intent.canceled":await h(t.data.object);break;default:console.log(`Unhandled event type: ${t.type}`)}return s.NextResponse.json({received:!0})}catch(e){return console.error("Webhook processing error:",e),s.NextResponse.json({error:"Webhook processing failed"},{status:500})}}async function y(e){console.log("handlePaymentSucceeded called:...........",JSON.stringify(e,null,2));try{let t=e.metadata.orderId;if(!t)return void console.error("No orderId in payment intent metadata");let r=await u.A.findById(t);if(!r)return void console.error(`Order not found: ${t}`);await r.markAsPaid(),r.stripePaymentIntentId=e.id,r.paymentMethod="stripe",r.stripePaymentDetails={paymentIntentId:e.id,amount:e.amount,currency:e.currency,status:e.status,created:new Date(1e3*e.created)},await r.save(),await l.A.findByIdAndUpdate(r.toolId,{$set:{paymentStatus:"completed",paidAt:new Date,status:"pending"}}),console.log(`Payment succeeded for order: ${t}`)}catch(e){console.error("Error handling payment succeeded:",e)}}async function g(e){try{let t=e.metadata.orderId;if(!t)return void console.error("No orderId in payment intent metadata");let r=await u.A.findById(t);if(!r)return void console.error(`Order not found: ${t}`);await r.markAsFailed(),r.stripePaymentDetails={paymentIntentId:e.id,amount:e.amount,currency:e.currency,status:e.status,created:new Date(1e3*e.created),failureReason:e.last_payment_error?.message},await r.save(),console.log(`Payment failed for order: ${t}`)}catch(e){console.error("Error handling payment failed:",e)}}async function h(e){try{let t=e.metadata.orderId;if(!t)return void console.error("No orderId in payment intent metadata");let r=await u.A.findById(t);if(!r)return void console.error(`Order not found: ${t}`);await r.cancel(),r.stripePaymentDetails={paymentIntentId:e.id,amount:e.amount,currency:e.currency,status:e.status,created:new Date(1e3*e.created)},await r.save(),console.log(`Payment canceled for order: ${t}`)}catch(e){console.error("Error handling payment canceled:",e)}}let f=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/stripe/webhook/route",pathname:"/api/stripe/webhook",filename:"route",bundlePath:"app/api/stripe/webhook/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:I,workUnitAsyncStorage:v,serverHooks:x}=f;function w(){return(0,o.patchFetch)({workAsyncStorage:I,workUnitAsyncStorage:v})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(56037),i=r.n(n);let a=new n.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:["text-generation","image-generation","video-generation","audio-generation","code-generation","data-analysis","productivity","design","marketing","education","research","other"]},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},publishedAt:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","published","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({status:1,isActive:1}),a.index({category:1,status:1}),a.index({tags:1,status:1}),a.index({submittedBy:1}),a.index({publishedAt:-1}),a.index({views:-1}),a.index({likes:-1}),a.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let o=i().models.Tool||i().model("Tool",a)},31098:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(56037),i=r.n(n);let a=new n.Schema({userId:{type:n.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},toolId:{type:n.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},type:{type:String,required:!0,enum:["launch_date_priority"],default:"launch_date_priority"},amount:{type:Number,required:[!0,"Amount is required"],min:[0,"Amount must be positive"]},currency:{type:String,required:!0,default:"CNY",enum:["CNY","USD"]},status:{type:String,required:!0,enum:["pending","completed","failed","cancelled","refunded"],default:"pending"},paymentMethod:{type:String,trim:!0},paymentIntentId:{type:String,trim:!0},paymentSessionId:{type:String,trim:!0},stripePaymentIntentId:{type:String,trim:!0},stripeCustomerId:{type:String,trim:!0},stripePaymentDetails:{paymentIntentId:String,amount:Number,currency:String,status:String,created:Date,failureReason:String},description:{type:String,required:[!0,"Description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},selectedLaunchDate:{type:Date,required:[!0,"Selected launch date is required"]},paidAt:{type:Date},cancelledAt:{type:Date},refundedAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({userId:1,createdAt:-1}),a.index({toolId:1}),a.index({status:1}),a.index({paymentIntentId:1}),a.index({paymentSessionId:1}),a.index({stripePaymentIntentId:1}),a.index({stripeCustomerId:1}),a.virtual("user",{ref:"User",localField:"userId",foreignField:"_id",justOne:!0}),a.virtual("tool",{ref:"Tool",localField:"toolId",foreignField:"_id",justOne:!0}),a.methods.markAsPaid=function(){return this.status="completed",this.paidAt=new Date,this.save()},a.methods.markAsFailed=function(){return this.status="failed",this.save()},a.methods.cancel=function(){return this.status="cancelled",this.cancelledAt=new Date,this.save()},a.methods.refund=function(){return this.status="refunded",this.refundedAt=new Date,this.save()};let o=i().models.Order||i().model("Order",a)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(56037),i=r.n(n);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let s=async function(){if(o.conn)return o.conn;o.promise||(o.promise=i().connect(a,{bufferCommands:!1}).then(e=>e));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},78335:()=>{},79171:(e,t,r)=>{"use strict";r.d(t,{kX:()=>n});let n={PRIORITY_LAUNCH:{displayPrice:99,stripeAmount:9900,currency:"CNY",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"CNY",stripeCurrency:"cny",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}};n.FREE_LAUNCH.description,n.FREE_LAUNCH.displayPrice,n.FREE_LAUNCH.features,n.PRIORITY_LAUNCH.description,n.PRIORITY_LAUNCH.displayPrice,n.PRIORITY_LAUNCH.features;let i={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}};i.FREE.value,i.FREE.label,i.FREEMIUM.value,i.FREEMIUM.label,i.PAID.value,i.PAID.label,i.FREE.value,i.FREE.label,i.FREEMIUM.value,i.FREEMIUM.label,i.PAID.value,i.PAID.label},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},96706:(e,t,r)=>{"use strict";r.d(t,{ZW:()=>c,bw:()=>d,f:()=>o,stripe:()=>a});var n=r(97877),i=r(79171);let a=new n.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-12-18.acacia",typescript:!0});async function o(e,t="cny",r={}){try{return await a.paymentIntents.create({amount:e,currency:t,metadata:r,automatic_payment_methods:{enabled:!0}})}catch(e){throw console.error("Error creating payment intent:",e),Error("Failed to create payment intent")}}async function s(e,t,r={}){try{return await a.customers.create({email:e,name:t,metadata:r})}catch(e){throw console.error("Error creating Stripe customer:",e),Error("Failed to create customer")}}async function d(e,t,r={}){try{let n=await a.customers.list({email:e,limit:1});if(n.data.length>0)return n.data[0];return await s(e,t,r)}catch(e){throw console.error("Error getting or creating Stripe customer:",e),Error("Failed to get or create customer")}}function c(e,t,r){try{return a.webhooks.constructEvent(e,t,r)}catch(e){throw console.error("Error constructing webhook event:",e),Error("Invalid webhook signature")}}i.kX.PRIORITY_LAUNCH.productName,i.kX.PRIORITY_LAUNCH.stripeAmount,i.kX.PRIORITY_LAUNCH.stripeCurrency,i.kX.PRIORITY_LAUNCH.description,i.kX.PRIORITY_LAUNCH.features}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4243,580,4999,7877],()=>r(9865));module.exports=n})();