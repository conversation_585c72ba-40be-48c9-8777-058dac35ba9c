(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5210],{646:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2713:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2731:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(5155);function a(e){let{size:s="md",className:t=""}=e;return(0,r.jsx)("div",{className:"flex justify-center items-center ".concat(t),children:(0,r.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[s]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},3717:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3786:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4186:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4416:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4478:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var r=t(5155);t(2115);var a=t(6874),l=t.n(a);let c=e=>{let{children:s}=e;return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("main",{className:"flex-1",children:s}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},4508:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var r=t(5155),a=t(2115),l=t(2108),c=t(5695),n=t(6874),i=t.n(n),d=t(4478),x=t(2731),o=t(9783),h=t(646),m=t(4186),u=t(4861),p=t(3717),g=t(7550),j=t(4616),b=t(2713),y=t(2657),N=t(9074),f=t(3786);let v=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},w=e=>{switch(e){case"approved":return"已通过";case"pending":return"审核中";case"rejected":return"已拒绝";case"draft":return"草稿";default:return e}},A=e=>{switch(e){case"approved":return(0,r.jsx)(h.A,{className:"h-4 w-4"});case"pending":return(0,r.jsx)(m.A,{className:"h-4 w-4"});case"rejected":return(0,r.jsx)(u.A,{className:"h-4 w-4"});case"draft":return(0,r.jsx)(p.A,{className:"h-4 w-4"});default:return null}};function k(){let{data:e,status:s}=(0,l.useSession)(),t=(0,c.useRouter)(),[n,m]=(0,a.useState)("all"),[u,k]=(0,a.useState)([]),[C,M]=(0,a.useState)(!0),[L,S]=(0,a.useState)("");(0,a.useEffect)(()=>{if("unauthenticated"===s)return void t.push("/");"authenticated"===s&&D()},[s,t]);let D=async()=>{try{M(!0),S("");let e=await fetch("/api/user/tools"),s=await e.json();s.success&&s.data?k(s.data.tools):S(s.message||"获取工具列表失败")}catch(e){S("网络错误，请重试")}finally{M(!1)}},_=u.filter(e=>"all"===n||e.status===n),z={total:u.length,draft:u.filter(e=>"draft"===e.status).length,approved:u.filter(e=>"approved"===e.status).length,pending:u.filter(e=>"pending"===e.status).length,rejected:u.filter(e=>"rejected"===e.status).length,totalViews:u.reduce((e,s)=>e+s.views,0),totalLikes:u.reduce((e,s)=>e+s.likes,0)};return"loading"===s||C?(0,r.jsx)(d.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(x.A,{size:"lg",className:"py-20"})})}):e?(0,r.jsx)(d.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(i(),{href:"/profile",className:"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(g.A,{className:"h-5 w-5"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"我提交的AI工具"})]}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"管理您提交的所有AI工具"})]}),(0,r.jsxs)(i(),{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(j.A,{className:"mr-2 h-5 w-5"}),"提交新工具"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(b.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总提交数"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:z.total})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(h.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"已通过"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:z.approved})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(y.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总浏览量"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:z.totalViews})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-8 w-8 text-red-600",children:"❤️"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总点赞数"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:z.totalLikes})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsxs)("button",{onClick:()=>m("all"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("all"===n?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["全部 (",z.total,")"]}),(0,r.jsxs)("button",{onClick:()=>m("draft"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("draft"===n?"bg-gray-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["草稿 (",z.draft,")"]}),(0,r.jsxs)("button",{onClick:()=>m("approved"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("approved"===n?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["已通过 (",z.approved,")"]}),(0,r.jsxs)("button",{onClick:()=>m("pending"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("pending"===n?"bg-yellow-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["审核中 (",z.pending,")"]}),(0,r.jsxs)("button",{onClick:()=>m("rejected"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("rejected"===n?"bg-red-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["已拒绝 (",z.rejected,")"]})]})}),L&&(0,r.jsx)(o.A,{message:L,onClose:()=>S(""),className:"mb-6"}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:_.length>0?(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:_.map(e=>(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name}),(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(v(e.status)),children:[A(e.status),(0,r.jsx)("span",{className:"ml-1",children:w(e.status)})]})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-3 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(N.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["提交于 ",new Date(e.submittedAt).toLocaleDateString("zh-CN")]})]}),e.publishedAt&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["发布于 ",new Date(e.publishedAt).toLocaleDateString("zh-CN")]})]}),"approved"===e.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[e.views," 浏览"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"❤️"}),(0,r.jsxs)("span",{children:[e.likes," 点赞"]})]})]})]}),"rejected"===e.status&&e.reviewNotes&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-red-800",children:[(0,r.jsx)("strong",{children:"拒绝原因："})," ",e.reviewNotes]})}),"draft"===e.status&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,r.jsxs)("p",{className:"text-sm text-blue-800 mb-2",children:[(0,r.jsx)("strong",{children:"下一步："})," 选择发布日期"]}),(0,r.jsx)(i(),{href:"/submit/launch-date/".concat(e._id),className:"inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors",children:"选择发布日期"})]}),"pending"===e.status&&e.launchOption&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,r.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:"发布选项："})}),(0,r.jsx)("span",{className:"px-2 py-1 rounded text-xs ".concat("paid"===e.launchOption?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800"),children:"paid"===e.launchOption?"优先发布":"免费发布"})]}),e.selectedLaunchDate&&(0,r.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:"计划发布："})}),(0,r.jsx)("span",{children:new Date(e.selectedLaunchDate).toLocaleDateString("zh-CN")})]}),e.paymentRequired&&(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:"支付状态："})}),(0,r.jsx)("span",{className:"px-2 py-1 rounded text-xs ".concat("completed"===e.paymentStatus?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"completed"===e.paymentStatus?"已支付":"待支付"})]}),(0,r.jsx)("div",{className:"flex justify-end mt-2",children:(0,r.jsxs)(i(),{href:"/submit/edit-launch-date/".concat(e._id),className:"inline-flex items-center px-3 py-1 bg-yellow-600 text-white text-xs rounded-lg hover:bg-yellow-700 transition-colors",children:[(0,r.jsx)(N.A,{className:"h-3 w-3 mr-1"}),"修改发布日期"]})})]})}),"approved"===e.status&&e.launchOption&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,r.jsxs)("div",{className:"text-sm text-green-800",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:"发布选项："})}),(0,r.jsx)("span",{className:"px-2 py-1 rounded text-xs ".concat("paid"===e.launchOption?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800"),children:"paid"===e.launchOption?"优先发布":"免费发布"})]}),e.selectedLaunchDate&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:"发布日期："})}),(0,r.jsx)("span",{children:new Date(e.selectedLaunchDate).toLocaleDateString("zh-CN")})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:["approved"===e.status&&(0,r.jsx)(i(),{href:"/tools/".concat(e._id),className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"查看详情",children:(0,r.jsx)(y.A,{className:"h-5 w-5"})}),"draft"===e.status&&!e.launchDateSelected&&(0,r.jsx)(i(),{href:"/submit/launch-date/".concat(e._id),className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"设定发布日期",children:(0,r.jsx)(N.A,{className:"h-5 w-5"})}),["pending","approved"].includes(e.status)&&e.launchDateSelected&&(0,r.jsx)(i(),{href:"/submit/edit-launch-date/".concat(e._id),className:"p-2 text-gray-400 hover:text-orange-600 transition-colors",title:"修改发布日期",children:(0,r.jsx)(N.A,{className:"h-5 w-5"})}),(0,r.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-green-600 transition-colors",title:"访问网站",children:(0,r.jsx)(f.A,{className:"h-5 w-5"})}),["draft","pending","rejected","approved","published"].includes(e.status)&&(0,r.jsx)(i(),{href:"/submit/edit/".concat(e._id),className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"published"===e.status?"编辑基础信息":"approved"===e.status?"编辑基础信息（不可修改URL）":"编辑工具信息",children:(0,r.jsx)(p.A,{className:"h-5 w-5"})})]})]})},e._id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(b.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"all"===n?"还没有提交任何工具":"没有".concat(w(n),"的工具")}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"all"===n?"开始提交您的第一个 AI 工具吧！":"尝试选择其他状态查看工具"}),"all"===n&&(0,r.jsxs)(i(),{href:"/submit",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"提交工具"]})]})})]})}):null}},4616:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5183:(e,s,t)=>{Promise.resolve().then(t.bind(t,4508))},5339:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5695:(e,s,t)=>{"use strict";var r=t(8999);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},7550:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9074:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9783:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var r=t(5155),a=t(5339),l=t(4416);function c(e){let{message:s,onClose:t,className:c=""}=e;return(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(c),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:s})}),t&&(0,r.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},9946:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var r=t(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),c=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},i=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let x=(0,r.forwardRef)((e,s)=>{let{color:t="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:c,className:x="",children:o,iconNode:h,...m}=e;return(0,r.createElement)("svg",{ref:s,...d,width:a,height:a,stroke:t,strokeWidth:c?24*Number(l)/Number(a):l,className:n("lucide",x),...!o&&!i(m)&&{"aria-hidden":"true"},...m},[...h.map(e=>{let[s,t]=e;return(0,r.createElement)(s,t)}),...Array.isArray(o)?o:[o]])}),o=(e,s)=>{let t=(0,r.forwardRef)((t,l)=>{let{className:i,...d}=t;return(0,r.createElement)(x,{ref:l,iconNode:s,className:n("lucide-".concat(a(c(e))),"lucide-".concat(e),i),...d})});return t.displayName=c(e),t}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,2108,8441,1684,7358],()=>s(5183)),_N_E=e.O()}]);