(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{3792:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var l=t(5155),r=t(2115),a=t(6874),i=t.n(a),n=t(4478),c=t(9824),d=t(4354),o=t(2731),x=t(9783),m=t(6063),g=t(5731),h=t(7924),u=t(1539),b=t(9074),p=t(4186),j=t(3109),f=t(8564);let N=[{_id:"1",name:"文本生成",slug:"text-generation",description:"AI tools for generating and editing text content",icon:"\uD83D\uDCDD",color:"#3B82F6",toolCount:25},{_id:"2",name:"图像生成",slug:"image-generation",description:"AI tools for creating and editing images",icon:"\uD83C\uDFA8",color:"#8B5CF6",toolCount:18},{_id:"3",name:"代码生成",slug:"code-generation",description:"AI tools for writing and debugging code",icon:"\uD83D\uDCBB",color:"#F59E0B",toolCount:12},{_id:"4",name:"数据分析",slug:"data-analysis",description:"AI tools for analyzing and visualizing data",icon:"\uD83D\uDCCA",color:"#06B6D4",toolCount:15}];function y(){let[e,s]=(0,r.useState)([]),[t,a]=(0,r.useState)([]),[y,v]=(0,r.useState)([]),[w,A]=(0,r.useState)(!0),[k,C]=(0,r.useState)(""),[S,I]=(0,r.useState)(!1);(0,r.useEffect)(()=>{_()},[]);let _=async()=>{try{A(!0),C("");let e=new Date,t=new Date(e);t.setHours(0,0,0,0);let l=new Date(e);l.setHours(23,59,59,999);let r=new Date;r.setDate(r.getDate()-7),r.setHours(0,0,0,0);let[i,n,c]=await Promise.all([g.u.getTools({status:"approved",limit:6,sort:"views",order:"desc"}),g.u.getTools({status:"approved",limit:20,sort:"publishedAt",order:"desc",dateFrom:t.toISOString(),dateTo:l.toISOString()}),g.u.getTools({status:"approved",limit:20,sort:"publishedAt",order:"desc",dateFrom:r.toISOString()})]);i.success&&i.data&&s(i.data.tools),n.success&&n.data&&a(n.data.tools.slice(0,6)),c.success&&c.data&&v(c.data.tools.slice(0,6))}catch(e){C("网络错误，请重试")}finally{A(!1)}};return w?(0,l.jsx)(n.A,{children:(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsx)(o.A,{size:"lg"})})}):(0,l.jsxs)(n.A,{children:[k&&(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8",children:(0,l.jsx)(x.A,{message:k,onClose:()=>C("")})}),(0,l.jsx)("section",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 py-20",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:["发现最好的",(0,l.jsx)("span",{className:"text-blue-600",children:" AI 工具"})]}),(0,l.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美工具。"}),(0,l.jsx)("div",{className:"max-w-2xl mx-auto mb-8",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("input",{type:"text",placeholder:"搜索 AI 工具、分类或功能...",className:"w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-lg"}),(0,l.jsx)(h.A,{className:"absolute left-4 top-4 h-6 w-6 text-gray-400"})]})}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,l.jsxs)(i(),{href:"/tools",className:"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,l.jsx)(u.A,{className:"mr-2 h-5 w-5"}),"浏览所有工具"]}),(0,l.jsx)(i(),{href:"/submit",className:"inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:"提交您的工具"})]})]})})}),t.length>0&&(0,l.jsx)("section",{className:"py-16 bg-gradient-to-r from-green-50 to-blue-50",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"text-center mb-12",children:[(0,l.jsxs)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,l.jsx)(b.A,{className:"inline-block mr-2 h-8 w-8 text-green-600"}),"今日发布"]}),(0,l.jsx)("p",{className:"text-lg text-gray-600",children:"今天刚刚发布的最新 AI 工具"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(e=>(0,l.jsx)(c.A,{tool:e,onLoginRequired:()=>I(!0)},e._id))})]})}),y.length>0&&(0,l.jsx)("section",{className:"py-16 bg-white",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"text-center mb-12",children:[(0,l.jsxs)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,l.jsx)(p.A,{className:"inline-block mr-2 h-8 w-8 text-blue-600"}),"最近发布"]}),(0,l.jsx)("p",{className:"text-lg text-gray-600",children:"过去一周内发布的新 AI 工具"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:y.map(e=>(0,l.jsx)(c.A,{tool:e,onLoginRequired:()=>I(!0)},e._id))}),(0,l.jsx)("div",{className:"text-center mt-8",children:(0,l.jsx)(i(),{href:"/tools?sort=publishedAt&order=desc",className:"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors",children:"查看更多最新工具"})})]})}),(0,l.jsx)("section",{className:"py-16 bg-gray-50",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"text-center mb-12",children:[(0,l.jsxs)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,l.jsx)(j.A,{className:"inline-block mr-2 h-8 w-8 text-blue-600"}),"热门工具"]}),(0,l.jsx)("p",{className:"text-lg text-gray-600",children:"最受欢迎和评价最高的 AI 工具"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,l.jsx)(c.A,{tool:e,onLoginRequired:()=>I(!0)},e._id))}),(0,l.jsx)("div",{className:"text-center mt-8",children:(0,l.jsx)(i(),{href:"/tools",className:"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors",children:"查看更多工具"})})]})}),(0,l.jsx)("section",{className:"py-16 bg-white",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"text-center mb-12",children:[(0,l.jsxs)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,l.jsx)(f.A,{className:"inline-block mr-2 h-8 w-8 text-blue-600"}),"热门分类"]}),(0,l.jsx)("p",{className:"text-lg text-gray-600",children:"按功能分类浏览 AI 工具"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:N.map(e=>(0,l.jsx)(d.A,{category:e},e._id))}),(0,l.jsx)("div",{className:"text-center mt-8",children:(0,l.jsx)(i(),{href:"/categories",className:"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors",children:"查看所有分类"})})]})}),(0,l.jsx)("section",{className:"py-16 bg-blue-600",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 text-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-4xl font-bold text-white mb-2",children:"500+"}),(0,l.jsx)("div",{className:"text-blue-100",children:"AI 工具"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-4xl font-bold text-white mb-2",children:"50+"}),(0,l.jsx)("div",{className:"text-blue-100",children:"工具分类"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-4xl font-bold text-white mb-2",children:"10K+"}),(0,l.jsx)("div",{className:"text-blue-100",children:"用户访问"})]})]})})}),(0,l.jsx)(m.A,{isOpen:S,onClose:()=>I(!1)})]})}},4354:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var l=t(5155);t(2115);var r=t(6874),a=t.n(r);let i=e=>{let{category:s}=e;return(0,l.jsx)(a(),{href:"/categories/".concat(s.slug),children:(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer",children:(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center text-2xl",style:{backgroundColor:s.color||"#3B82F6"},children:(0,l.jsx)("span",{className:"text-white",children:s.icon||"\uD83D\uDD27"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:s.name}),(0,l.jsxs)("p",{className:"text-sm text-gray-500",children:[s.toolCount," 个工具"]})]})]}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:s.description})]})})})}},6063:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var l=t(5155),r=t(2115),a=t(2108),i=t(9911);function n(e){let{isOpen:s,onClose:t}=e,[n,c]=(0,r.useState)("method"),[d,o]=(0,r.useState)(""),[x,m]=(0,r.useState)(""),[g,h]=(0,r.useState)(!1),[u,b]=(0,r.useState)(""),p=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",t=document.createElement("div");t.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===s?"bg-green-500":"bg-red-500"),t.textContent=e,document.body.appendChild(t),setTimeout(()=>document.body.removeChild(t),3e3)},j=()=>{c("method"),o(""),m(""),b(""),t()},f=async e=>{try{h(!0),await (0,a.signIn)(e,{callbackUrl:"/"})}catch(e){p("登录失败，请稍后重试","error")}finally{h(!1)}},N=async()=>{if(!d)return void b("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(d))return void b("请输入有效的邮箱地址");b(""),h(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:d})}),s=await e.json();s.success?(m(s.token),c("code"),p("验证码已发送，请查看您的邮箱")):p(s.error||"发送失败，请稍后重试","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{h(!1)}},y=async e=>{if(6===e.length){h(!0);try{let s=await (0,a.signIn)("email-code",{email:d,code:e,token:x,redirect:!1});(null==s?void 0:s.ok)?(p("登录成功，欢迎回来！"),j()):p((null==s?void 0:s.error)||"验证码错误","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{h(!1)}}},v=(e,s)=>{if(s.length>1)return;let t=document.querySelectorAll(".code-input");if(t[e].value=s,s&&e<5){var l;null==(l=t[e+1])||l.focus()}let r=Array.from(t).map(e=>e.value).join("");6===r.length&&y(r)};return s?(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:j}),(0,l.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===n&&"登录 AI Tools Directory","email"===n&&"邮箱登录","code"===n&&"输入验证码"]}),(0,l.jsx)("button",{onClick:j,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,l.jsx)(i.QCr,{})})]}),(0,l.jsxs)("div",{className:"p-6",children:["method"===n&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>f("google"),disabled:g,children:[(0,l.jsx)(i.DSS,{}),"使用 Google 登录"]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>f("github"),disabled:g,children:[(0,l.jsx)(i.hL4,{}),"使用 GitHub 登录"]})]}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,l.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,l.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,l.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>c("email"),children:[(0,l.jsx)(i.maD,{}),"使用邮箱登录"]})]}),"email"===n&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,l.jsx)("input",{type:"email",value:d,onChange:e=>o(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&N(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),u&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:N,disabled:g,children:g?"发送中...":"发送验证码"}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]}),"code"===n&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",d," 的6位验证码"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,l.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,l.jsx)("input",{type:"text",maxLength:1,onChange:s=>v(e,s.target.value),disabled:g,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("email"),children:"重新发送验证码"}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]})]})]})]}):null}},9824:(e,s,t)=>{"use strict";t.d(s,{A:()=>x});var l=t(5155);t(2115);var r=t(6874),a=t.n(r),i=t(3786),n=t(2657),c=t(1976),d=t(4601),o=t(3467);let x=e=>{let{tool:s,onLoginRequired:t,onUnlike:r,isInLikedPage:x=!1}=e;return(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",children:(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[s.logo?(0,l.jsx)("img",{src:s.logo,alt:s.name,className:"w-12 h-12 rounded-lg object-cover"}):(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-white font-bold text-lg",children:s.name.charAt(0).toUpperCase()})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:s.name}),(0,l.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat((0,o.Ef)(s.pricing)),children:(0,o.mV)(s.pricing)})]})]}),(0,l.jsx)("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,l.jsx)(i.A,{className:"h-5 w-5"})})]}),(0,l.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:s.description}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[s.tags.slice(0,3).map((e,s)=>(0,l.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e},s)),s.tags.length>3&&(0,l.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:["+",s.tags.length-3]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(n.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{children:s.views})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(c.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{children:s.likes})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(d.A,{toolId:s._id,initialLikes:s.likes,onLoginRequired:t,onUnlike:r,isInLikedPage:x}),(0,l.jsx)(a(),{href:"/tools/".concat(s._id),className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors",children:"查看详情"})]})]})]})})}},9919:(e,s,t)=>{Promise.resolve().then(t.bind(t,3792))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,5211,2562,8441,1684,7358],()=>s(9919)),_N_E=e.O()}]);