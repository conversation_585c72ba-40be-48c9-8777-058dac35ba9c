(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8664],{1852:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var a=t(5155),l=t(2115),r=t(6874),i=t.n(r),n=t(5695),c=t(4478),d=t(2731),o=t(9783),x=t(4601),m=t(2108),h=t(9911);function u(e){let{toolId:s,onLoginRequired:t}=e,{data:r}=(0,m.useSession)(),[i,n]=(0,l.useState)([]),[c,d]=(0,l.useState)(""),[o,x]=(0,l.useState)(null),[u,g]=(0,l.useState)(""),[p,j]=(0,l.useState)(!1),[b,y]=(0,l.useState)(!1),f=async()=>{j(!0);try{let e=await fetch("/api/tools/".concat(s,"/comments"));if(e.ok){let s=await e.json();s.success&&n(s.data.comments)}}catch(e){console.error("Failed to fetch comments:",e)}finally{j(!1)}};(0,l.useEffect)(()=>{f()},[s]);let N=async()=>{if(!r){null==t||t();return}if(c.trim()){y(!0);try{let e=await fetch("/api/tools/".concat(s,"/comments"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:c.trim()})});if(e.ok)(await e.json()).success&&(d(""),f());else{let s=await e.json();console.error("Comment submission failed:",s.message)}}catch(e){console.error("Comment submission error:",e)}finally{y(!1)}}},v=async e=>{if(!r){null==t||t();return}if(u.trim()){y(!0);try{let t=await fetch("/api/tools/".concat(s,"/comments"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:u.trim(),parentId:e})});if(t.ok)(await t.json()).success&&(g(""),x(null),f());else{let e=await t.json();console.error("Reply submission failed:",e.message)}}catch(e){console.error("Reply submission error:",e)}finally{y(!1)}}},w=e=>{let s=new Date(e),t=Math.floor((new Date().getTime()-s.getTime())/36e5);return t<1?"刚刚":t<24?"".concat(t,"小时前"):t<168?"".concat(Math.floor(t/24),"天前"):s.toLocaleDateString("zh-CN")},k=e=>{let{comment:s,isReply:t=!1}=e;return(0,a.jsx)("div",{className:"".concat(t?"ml-8 border-l-2 border-gray-100 pl-4":""),children:(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:s.userId.image?(0,a.jsx)("img",{src:s.userId.image,alt:s.userId.name,className:"w-8 h-8 rounded-full"}):(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,a.jsx)(h.x$1,{className:"w-4 h-4 text-gray-600"})})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:"font-medium text-gray-900",children:s.userId.name}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:w(s.createdAt)})]}),(0,a.jsx)("p",{className:"text-gray-700 mb-2",children:s.content}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:!t&&(0,a.jsxs)("button",{onClick:()=>x(o===s._id?null:s._id),className:"text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1",children:[(0,a.jsx)(h.w1Z,{className:"w-3 h-3"}),"回复"]})}),o===s._id&&(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("textarea",{value:u,onChange:e=>g(e.target.value),placeholder:"写下你的回复...",className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:3,maxLength:1e3}),(0,a.jsxs)("div",{className:"flex justify-end gap-2 mt-2",children:[(0,a.jsx)("button",{onClick:()=>{x(null),g("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:"取消"}),(0,a.jsx)("button",{onClick:()=>v(s._id),disabled:b||!u.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:b?"发送中...":"发送"})]})]}),s.replies&&s.replies.length>0&&(0,a.jsx)("div",{className:"mt-4 space-y-4",children:s.replies.map(e=>(0,a.jsx)(k,{comment:e,isReply:!0},e._id))})]})]})})};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["评论 (",i.length,")"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("textarea",{value:c,onChange:e=>d(e.target.value),placeholder:r?"写下你的评论...":"请先登录后评论",className:"w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4,maxLength:1e3,disabled:!r}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[c.length,"/1000"]}),(0,a.jsx)("button",{onClick:N,disabled:b||!c.trim()||!r,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:b?"发送中...":"发表评论"})]})]}),p?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-2",children:"加载评论中..."})]}):0===i.length?(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"暂无评论，来发表第一条评论吧！"})}):(0,a.jsx)("div",{className:"space-y-6",children:i.map(e=>(0,a.jsx)(k,{comment:e},e._id))})]})}var g=t(6063),p=t(5731),j=t(3467),b=t(7550),y=t(5868),f=t(2657),N=t(1976),v=t(6516),w=t(3332),k=t(3786);function C(){var e;let s=(0,n.useParams)(),[t,r]=(0,l.useState)(null),[m,h]=(0,l.useState)([]),[C,S]=(0,l.useState)(!0),[A,_]=(0,l.useState)(""),[T,E]=(0,l.useState)(!1);(0,l.useEffect)(()=>{s.id&&I(s.id)},[s.id]);let I=async e=>{try{S(!0),_("");let s=await p.u.getTool(e);s.success&&s.data?(r(s.data),L(s.data.category)):_(s.error||"工具不存在")}catch(e){_("网络错误，请重试")}finally{S(!1)}},L=async e=>{try{let t=await p.u.getTools({category:e,status:"approved",limit:3});if(t.success&&t.data){let e=t.data.tools.filter(e=>e._id!==s.id);h(e.slice(0,3))}}catch(e){}};return C?(0,a.jsx)(c.A,{children:(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(d.A,{size:"lg"})})}):A||!t?(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)(o.A,{message:A||"工具不存在",onClose:()=>_("")}),(0,a.jsx)("div",{className:"text-center mt-8",children:(0,a.jsxs)(i(),{href:"/tools",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(b.A,{className:"w-4 h-4 mr-2"}),"返回工具目录"]})})]})}):(0,a.jsxs)(c.A,{children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6",children:[(0,a.jsx)(i(),{href:"/",className:"hover:text-blue-600",children:"首页"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)(i(),{href:"/tools",className:"hover:text-blue-600",children:"工具目录"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)("span",{className:"text-gray-900",children:t.name})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)(i(),{href:"/tools",className:"inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"返回工具目录"]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[t.logo?(0,a.jsx)("img",{src:t.logo,alt:t.name,className:"w-16 h-16 rounded-lg object-cover"}):(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-2xl",children:t.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:t.name}),t.tagline&&(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-3",children:t.tagline}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat((0,j.Ef)(t.pricing)),children:[(0,a.jsx)(y.A,{className:"mr-1 h-4 w-4"}),(0,j.mV)(t.pricing)]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[t.views||0," 浏览"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(N.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[t.likes||0," 喜欢"]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{toolId:t._id,initialLikes:t.likes,onLoginRequired:()=>E(!0)}),(0,a.jsx)("button",{className:"p-2 text-gray-400 hover:text-blue-500 transition-colors",children:(0,a.jsx)(v.A,{className:"h-5 w-5"})})]})]}),(0,a.jsx)("p",{className:"text-gray-600 text-lg leading-relaxed mb-6",children:t.description}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:null==(e=t.tags)?void 0:e.map((e,s)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer",children:[(0,a.jsx)(w.A,{className:"mr-1 h-3 w-3"}),e]},s))}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(k.A,{className:"mr-2 h-5 w-5"}),"访问 ",t.name]}),(0,a.jsx)(x.A,{toolId:t._id,initialLikes:t.likes,onLoginRequired:()=>E(!0)})]})]})}),(0,a.jsxs)("div",{className:"lg:col-span-1",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"工具信息"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"分类"}),(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:"文本生成"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"价格模式"}),(0,a.jsx)("span",{className:"px-2 py-1 rounded text-sm font-medium ".concat((0,j.Ef)(t.pricing)),children:(0,j.mV)(t.pricing)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"提交日期"}),(0,a.jsx)("span",{className:"text-gray-900",children:t.submittedAt||t.createdAt})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"提交者"}),(0,a.jsx)("span",{className:"text-gray-900",children:t.submittedBy||"未知"})]})]})]}),m.length>0&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"相关工具"}),(0,a.jsx)("div",{className:"space-y-4",children:m.map(e=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow",children:(0,a.jsxs)(i(),{href:"/tools/".concat(e._id),children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 hover:text-blue-600 mb-1",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsx)("span",{className:"px-2 py-1 rounded ".concat((0,j.Ef)(e.pricing)),children:(0,j.mV)(e.pricing)}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{children:[e.views||0," 浏览"]}),(0,a.jsxs)("span",{children:[e.likes||0," 喜欢"]})]})]})]})},e._id))})]}),(0,a.jsx)("div",{className:"mt-12",children:(0,a.jsx)(u,{toolId:t._id,onLoginRequired:()=>E(!0)})})]})]})]}),(0,a.jsx)(g.A,{isOpen:T,onClose:()=>E(!1)})]})}},6063:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var a=t(5155),l=t(2115),r=t(2108),i=t(9911);function n(e){let{isOpen:s,onClose:t}=e,[n,c]=(0,l.useState)("method"),[d,o]=(0,l.useState)(""),[x,m]=(0,l.useState)(""),[h,u]=(0,l.useState)(!1),[g,p]=(0,l.useState)(""),j=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",t=document.createElement("div");t.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===s?"bg-green-500":"bg-red-500"),t.textContent=e,document.body.appendChild(t),setTimeout(()=>document.body.removeChild(t),3e3)},b=()=>{c("method"),o(""),m(""),p(""),t()},y=async e=>{try{u(!0),await (0,r.signIn)(e,{callbackUrl:"/"})}catch(e){j("登录失败，请稍后重试","error")}finally{u(!1)}},f=async()=>{if(!d)return void p("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(d))return void p("请输入有效的邮箱地址");p(""),u(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:d})}),s=await e.json();s.success?(m(s.token),c("code"),j("验证码已发送，请查看您的邮箱")):j(s.error||"发送失败，请稍后重试","error")}catch(e){j("网络错误，请检查网络连接","error")}finally{u(!1)}},N=async e=>{if(6===e.length){u(!0);try{let s=await (0,r.signIn)("email-code",{email:d,code:e,token:x,redirect:!1});(null==s?void 0:s.ok)?(j("登录成功，欢迎回来！"),b()):j((null==s?void 0:s.error)||"验证码错误","error")}catch(e){j("网络错误，请检查网络连接","error")}finally{u(!1)}}},v=(e,s)=>{if(s.length>1)return;let t=document.querySelectorAll(".code-input");if(t[e].value=s,s&&e<5){var a;null==(a=t[e+1])||a.focus()}let l=Array.from(t).map(e=>e.value).join("");6===l.length&&N(l)};return s?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:b}),(0,a.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===n&&"登录 AI Tools Directory","email"===n&&"邮箱登录","code"===n&&"输入验证码"]}),(0,a.jsx)("button",{onClick:b,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(i.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-6",children:["method"===n&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>y("google"),disabled:h,children:[(0,a.jsx)(i.DSS,{}),"使用 Google 登录"]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>y("github"),disabled:h,children:[(0,a.jsx)(i.hL4,{}),"使用 GitHub 登录"]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>c("email"),children:[(0,a.jsx)(i.maD,{}),"使用邮箱登录"]})]}),"email"===n&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,a.jsx)("input",{type:"email",value:d,onChange:e=>o(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&f(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),g&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:f,disabled:h,children:h?"发送中...":"发送验证码"}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]}),"code"===n&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",d," 的6位验证码"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,a.jsx)("input",{type:"text",maxLength:1,onChange:s=>v(e,s.target.value),disabled:h,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("email"),children:"重新发送验证码"}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]})]})]})]}):null}},8985:(e,s,t)=>{Promise.resolve().then(t.bind(t,1852))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,592,2562,8441,1684,7358],()=>s(8985)),_N_E=e.O()}]);