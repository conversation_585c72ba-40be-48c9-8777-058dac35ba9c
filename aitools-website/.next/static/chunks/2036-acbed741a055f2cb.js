"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2036],{2731:(e,s,t)=>{t.d(s,{A:()=>r});var l=t(5155);function r(e){let{size:s="md",className:t=""}=e;return(0,l.jsx)("div",{className:"flex justify-center items-center ".concat(t),children:(0,l.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[s]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},3467:(e,s,t)=>{t.d(s,{$g:()=>o,Ef:()=>n,Y$:()=>i,kX:()=>l,mV:()=>d,tF:()=>x,v4:()=>c,vS:()=>r});let l={PRIORITY_LAUNCH:{displayPrice:99,stripeAmount:9900,currency:"CNY",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"CNY",stripeCurrency:"cny",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},r=[{id:"free",title:"免费发布",description:l.FREE_LAUNCH.description,price:l.FREE_LAUNCH.displayPrice,features:l.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:l.PRIORITY_LAUNCH.description,price:l.PRIORITY_LAUNCH.displayPrice,features:l.PRIORITY_LAUNCH.features,recommended:!0}],a={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},c=[{value:"",label:"所有价格"},{value:a.FREE.value,label:a.FREE.label},{value:a.FREEMIUM.value,label:a.FREEMIUM.label},{value:a.PAID.value,label:a.PAID.label}],i=[{value:a.FREE.value,label:a.FREE.label},{value:a.FREEMIUM.value,label:a.FREEMIUM.label},{value:a.PAID.value,label:a.PAID.label}],n=e=>{switch(e){case a.FREE.value:return a.FREE.color;case a.FREEMIUM.value:return a.FREEMIUM.color;case a.PAID.value:return a.PAID.color;default:return"bg-gray-100 text-gray-800"}},d=e=>{switch(e){case a.FREE.value:return a.FREE.label;case a.FREEMIUM.value:return a.FREEMIUM.label;case a.PAID.value:return a.PAID.label;default:return e}},o=e=>0===e?"免费":"\xa5".concat(e),x=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:s.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4478:(e,s,t)=>{t.d(s,{A:()=>c});var l=t(5155);t(2115);var r=t(6874),a=t.n(r);let c=e=>{let{children:s}=e;return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,l.jsx)("main",{className:"flex-1",children:s}),(0,l.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,l.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,l.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,l.jsxs)("ul",{className:"space-y-2",children:[(0,l.jsx)("li",{children:(0,l.jsx)(a(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,l.jsx)("li",{children:(0,l.jsx)(a(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,l.jsx)("li",{children:(0,l.jsx)(a(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,l.jsxs)("ul",{className:"space-y-2",children:[(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,l.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,l.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},5131:(e,s,t)=>{t.d(s,{A:()=>d});var l=t(5155),r=t(2115),a=t(4416),c=t(7924),i=t(3332),n=t(8146);function d(e){let{selectedTags:s,onTagsChange:t,maxTags:d=n.z}=e,[o,x]=(0,r.useState)(""),[u,m]=(0,r.useState)(!1),h=e=>{s.includes(e)?t(s.filter(s=>s!==e)):s.length<d&&t([...s,e])},b=e=>{t(s.filter(s=>s!==e))},g=n.MV.filter(e=>e.toLowerCase().includes(o.toLowerCase())&&!s.includes(e));return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"选择标签"}),(0,l.jsxs)("span",{className:"text-sm text-gray-500",children:["已选择 ",s.length,"/",d," 个标签"]})]}),s.length>0&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:"已选择的标签："}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:s.map(e=>(0,l.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[e,(0,l.jsx)("button",{type:"button",onClick:()=>b(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,l.jsx)(a.A,{className:"h-3 w-3"})})]},e))})]}),(0,l.jsx)("div",{className:"space-y-3",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["选择标签（最多",d,"个）"]}),(0,l.jsxs)("div",{className:"relative mb-3",children:[(0,l.jsx)("input",{type:"text",placeholder:"搜索标签...",value:o,onChange:e=>x(e.target.value),onFocus:()=>m(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,l.jsx)(c.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(u||o)&&(0,l.jsx)("div",{className:"relative",children:(0,l.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:g.length>0?(0,l.jsxs)("div",{className:"p-2",children:[(0,l.jsx)("div",{className:"grid grid-cols-1 gap-1",children:g.map(e=>{let t=s.length>=d;return(0,l.jsx)("button",{type:"button",onClick:()=>{h(e),x(""),m(!1)},disabled:t,className:"\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ".concat(t?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700","\n                            "),children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(i.A,{className:"h-3 w-3 mr-2 text-gray-400"}),e]})},e)})}),g.length>50&&(0,l.jsxs)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:["找到 ",g.length," 个匹配标签"]})]}):(0,l.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:o?"未找到匹配的标签":"开始输入以搜索标签"})})})]})}),(u||o)&&(0,l.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{m(!1),x("")}}),s.length>=d&&(0,l.jsxs)("p",{className:"text-sm text-amber-600",children:["最多只能选择",d,"个标签"]})]})}},6063:(e,s,t)=>{t.d(s,{A:()=>i});var l=t(5155),r=t(2115),a=t(2108),c=t(9911);function i(e){let{isOpen:s,onClose:t}=e,[i,n]=(0,r.useState)("method"),[d,o]=(0,r.useState)(""),[x,u]=(0,r.useState)(""),[m,h]=(0,r.useState)(!1),[b,g]=(0,r.useState)(""),p=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",t=document.createElement("div");t.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===s?"bg-green-500":"bg-red-500"),t.textContent=e,document.body.appendChild(t),setTimeout(()=>document.body.removeChild(t),3e3)},y=()=>{n("method"),o(""),u(""),g(""),t()},j=async e=>{try{h(!0),await (0,a.signIn)(e,{callbackUrl:"/"})}catch(e){p("登录失败，请稍后重试","error")}finally{h(!1)}},v=async()=>{if(!d)return void g("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(d))return void g("请输入有效的邮箱地址");g(""),h(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:d})}),s=await e.json();s.success?(u(s.token),n("code"),p("验证码已发送，请查看您的邮箱")):p(s.error||"发送失败，请稍后重试","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{h(!1)}},f=async e=>{if(6===e.length){h(!0);try{let s=await (0,a.signIn)("email-code",{email:d,code:e,token:x,redirect:!1});(null==s?void 0:s.ok)?(p("登录成功，欢迎回来！"),y()):p((null==s?void 0:s.error)||"验证码错误","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{h(!1)}}},N=(e,s)=>{if(s.length>1)return;let t=document.querySelectorAll(".code-input");if(t[e].value=s,s&&e<5){var l;null==(l=t[e+1])||l.focus()}let r=Array.from(t).map(e=>e.value).join("");6===r.length&&f(r)};return s?(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:y}),(0,l.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===i&&"登录 AI Tools Directory","email"===i&&"邮箱登录","code"===i&&"输入验证码"]}),(0,l.jsx)("button",{onClick:y,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,l.jsx)(c.QCr,{})})]}),(0,l.jsxs)("div",{className:"p-6",children:["method"===i&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>j("google"),disabled:m,children:[(0,l.jsx)(c.DSS,{}),"使用 Google 登录"]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>j("github"),disabled:m,children:[(0,l.jsx)(c.hL4,{}),"使用 GitHub 登录"]})]}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,l.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,l.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,l.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>n("email"),children:[(0,l.jsx)(c.maD,{}),"使用邮箱登录"]})]}),"email"===i&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,l.jsx)("input",{type:"email",value:d,onChange:e=>o(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&v(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),b&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:v,disabled:m,children:m?"发送中...":"发送验证码"}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>n("method"),children:"返回"})]})]}),"code"===i&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",d," 的6位验证码"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,l.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,l.jsx)("input",{type:"text",maxLength:1,onChange:s=>N(e,s.target.value),disabled:m,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>n("email"),children:"重新发送验证码"}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>n("method"),children:"返回"})]})]})]})]})]}):null}},8146:(e,s,t)=>{t.d(s,{MV:()=>l,z:()=>r});let l=["AI助手","ChatGPT","对话AI","智能问答","语言模型","写作助手","内容生成","文案创作","博客写作","营销文案","图像生成","图像编辑","AI绘画","头像生成","背景移除","视频生成","视频编辑","视频剪辑","短视频制作","视频字幕","语音合成","语音识别","音乐生成","语音转文字","文字转语音","代码生成","代码补全","代码审查","开发助手","低代码平台","数据分析","数据可视化","商业智能","机器学习","深度学习","办公自动化","文档处理","项目管理","团队协作","笔记工具","UI设计","Logo设计","网页设计","平面设计","原型设计","SEO优化","社交媒体营销","邮件营销","内容营销","市场分析","机器翻译","实时翻译","文档翻译","语音翻译"],r=3}}]);