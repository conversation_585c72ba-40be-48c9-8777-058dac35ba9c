"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2562],{2731:(e,t,r)=>{r.d(t,{A:()=>a});var s=r(5155);function a(e){let{size:t="md",className:r=""}=e;return(0,s.jsx)("div",{className:"flex justify-center items-center ".concat(r),children:(0,s.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},3467:(e,t,r)=>{r.d(t,{$g:()=>d,Ef:()=>o,Y$:()=>n,kX:()=>s,mV:()=>i,tF:()=>u,v4:()=>c,vS:()=>a});let s={PRIORITY_LAUNCH:{displayPrice:99,stripeAmount:9900,currency:"CNY",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"CNY",stripeCurrency:"cny",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},a=[{id:"free",title:"免费发布",description:s.FREE_LAUNCH.description,price:s.FREE_LAUNCH.displayPrice,features:s.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:s.PRIORITY_LAUNCH.description,price:s.PRIORITY_LAUNCH.displayPrice,features:s.PRIORITY_LAUNCH.features,recommended:!0}],l={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},c=[{value:"",label:"所有价格"},{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],n=[{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],o=e=>{switch(e){case l.FREE.value:return l.FREE.color;case l.FREEMIUM.value:return l.FREEMIUM.color;case l.PAID.value:return l.PAID.color;default:return"bg-gray-100 text-gray-800"}},i=e=>{switch(e){case l.FREE.value:return l.FREE.label;case l.FREEMIUM.value:return l.FREEMIUM.label;case l.PAID.value:return l.PAID.label;default:return e}},d=e=>0===e?"免费":"\xa5".concat(e),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4478:(e,t,r)=>{r.d(t,{A:()=>c});var s=r(5155);r(2115);var a=r(6874),l=r.n(a);let c=e=>{let{children:t}=e;return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("main",{className:"flex-1",children:t}),(0,s.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,s.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,s.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,s.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},4601:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(5155),a=r(2115),l=r(2108),c=r(9911);function n(e){let{toolId:t,initialLikes:r=0,initialLiked:n=!1,onLoginRequired:o,onUnlike:i,isInLikedPage:d=!1}=e,{data:u}=(0,l.useSession)(),[h,m]=(0,a.useState)(n),[x,b]=(0,a.useState)(r),[g,p]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=async()=>{try{let e=await fetch("/api/tools/".concat(t,"/like"));if(e.ok){let t=await e.json();t.success&&(m(t.data.liked),b(t.data.likes))}}catch(e){console.error("Failed to fetch like status:",e)}};u&&e()},[t,u]);let y=async()=>{if(!u){null==o||o();return}if(!g){p(!0);try{let e=await fetch("/api/tools/".concat(t,"/like"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(d?{forceUnlike:!0}:{})});if(e.ok){let r=await e.json();if(r.success){let e=r.data.liked;m(e),b(r.data.likes),!e&&i&&i(t)}}else{let t=await e.json();console.error("Like failed:",t.message)}}catch(e){console.error("Like request failed:",e)}finally{p(!1)}}};return(0,s.jsxs)("button",{onClick:y,disabled:g,className:"\n        flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200\n        ".concat(h?"bg-red-50 text-red-600 hover:bg-red-100":"bg-gray-50 text-gray-600 hover:bg-gray-100","\n        ").concat(g?"opacity-50 cursor-not-allowed":"hover:scale-105","\n        border border-gray-200 hover:border-gray-300\n      "),children:[h?(0,s.jsx)(c.Mbv,{className:"w-4 h-4 text-red-500"}):(0,s.jsx)(c.sOK,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:x>0?x:""})]})}},5731:(e,t,r)=>{r.d(t,{u:()=>l});let s=r(9509).env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class a{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let r="".concat(this.baseURL).concat(e),s={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(r,s),l=await a.json();if(!a.ok)throw Error(l.error||"HTTP error! status: ".concat(a.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[r,s]=e;void 0!==s&&t.append(r,s.toString())});let r=t.toString();return this.request("/tools".concat(r?"?".concat(r):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[r,s]=e;void 0!==s&&t.append(r,s.toString())});let r=t.toString();return this.request("/user/liked-tools".concat(r?"?".concat(r):""))}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[r,s]=e;void 0!==s&&t.append(r,s.toString())});let r=t.toString();return this.request("/admin/tools".concat(r?"?".concat(r):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=s){this.baseURL=e}}let l=new a},9783:(e,t,r)=>{r.d(t,{A:()=>c});var s=r(5155),a=r(5339),l=r(4416);function c(e){let{message:t,onClose:r,className:c=""}=e;return(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(c),children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("p",{className:"text-red-800 text-sm",children:t})}),r&&(0,s.jsx)("button",{onClick:r,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,s.jsx)(l.A,{className:"w-4 h-4"})})]})})}}}]);